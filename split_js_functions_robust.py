#!/usr/bin/env python3
"""
JavaScript 函数拆分脚本 - 健壮版
基于 Gemini 反馈的改进版本，解决正则表达式字面量、嵌套项等问题
"""

import re
import os
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class JSParser:
    def __init__(self, content: str):
        self.content = content
        self.length = len(content)
        self.pos = 0
        
    def peek(self, offset: int = 0) -> str:
        """查看指定位置的字符"""
        pos = self.pos + offset
        return self.content[pos] if pos < self.length else ''
    
    def advance(self, count: int = 1) -> str:
        """前进指定字符数并返回当前字符"""
        char = self.peek()
        self.pos += count
        return char
    
    def skip_whitespace(self):
        """跳过空白字符"""
        while self.pos < self.length and self.content[self.pos].isspace():
            self.pos += 1
    
    def get_previous_non_whitespace_char(self) -> str:
        """获取前一个非空白字符"""
        pos = self.pos - 1
        while pos >= 0 and self.content[pos].isspace():
            pos -= 1
        return self.content[pos] if pos >= 0 else ''
    
    def skip_string(self, quote_char: str) -> bool:
        """跳过字符串内容，返回是否成功"""
        if self.peek() != quote_char:
            return False
            
        self.advance()  # 跳过开始引号
        
        while self.pos < self.length:
            char = self.peek()
            if char == '\\':
                self.advance(2)  # 跳过转义字符
            elif char == quote_char:
                self.advance()  # 跳过结束引号
                return True
            else:
                self.advance()
        
        return False  # 未找到结束引号
    
    def skip_regex_literal(self) -> bool:
        """跳过正则表达式字面量"""
        if self.peek() != '/':
            return False
        
        # 检查前一个字符，判断是否为正则表达式
        prev_char = self.get_previous_non_whitespace_char()
        regex_start_chars = '(=,[!&|?{};:+*-/%<>^~'
        
        # 检查是否在 return 语句后
        context_before = self.content[max(0, self.pos - 10):self.pos].strip()
        if context_before.endswith('return'):
            # return /pattern/ 的情况
            pass
        elif prev_char and prev_char not in regex_start_chars:
            # 如果前面不是这些字符，很可能是除法运算符
            return False
        
        self.advance()  # 跳过开始的 /
        
        while self.pos < self.length:
            char = self.peek()
            if char == '\\':
                self.advance(2)  # 跳过转义字符
            elif char == '/':
                self.advance()  # 跳过结束的 /
                # 跳过可能的标志位 (g, i, m, etc.)
                while self.pos < self.length and self.peek().isalpha():
                    self.advance()
                return True
            elif char == '\n':
                # 正则表达式不能跨行（除非转义）
                return False
            else:
                self.advance()
        
        return False  # 未找到结束的 /
    
    def skip_template_literal(self) -> bool:
        """跳过模板字符串（反引号）"""
        if self.peek() != '`':
            return False
            
        self.advance()  # 跳过开始反引号
        
        while self.pos < self.length:
            char = self.peek()
            if char == '\\':
                self.advance(2)  # 跳过转义字符
            elif char == '$' and self.peek(1) == '{':
                # 处理模板表达式 ${...}
                self.advance(2)  # 跳过 ${
                brace_count = 1
                while self.pos < self.length and brace_count > 0:
                    char = self.peek()
                    if char in ['"', "'", '`']:
                        if not self.skip_string(char):
                            return False
                    elif char == '/':
                        if not self.skip_regex_literal():
                            self.advance()
                    elif char == '{':
                        brace_count += 1
                        self.advance()
                    elif char == '}':
                        brace_count -= 1
                        self.advance()
                    else:
                        self.advance()
            elif char == '`':
                self.advance()  # 跳过结束反引号
                return True
            else:
                self.advance()
        
        return False  # 未找到结束反引号
    
    def skip_comment(self) -> bool:
        """跳过注释"""
        if self.peek() == '/' and self.peek(1) == '/':
            # 单行注释
            while self.pos < self.length and self.peek() != '\n':
                self.advance()
            return True
        elif self.peek() == '/' and self.peek(1) == '*':
            # 多行注释
            self.advance(2)
            while self.pos < self.length - 1:
                if self.peek() == '*' and self.peek(1) == '/':
                    self.advance(2)
                    return True
                self.advance()
        return False
    
    def find_matching_brace(self, start_pos: int) -> Optional[int]:
        """找到匹配的大括号位置 - 改进版"""
        self.pos = start_pos
        
        # 找到第一个开括号
        while self.pos < self.length and self.peek() != '{':
            self.advance()
        
        if self.pos >= self.length:
            return None
            
        brace_count = 1
        self.advance()  # 跳过第一个 {
        
        while self.pos < self.length and brace_count > 0:
            char = self.peek()
            
            if char in ['"', "'"]:
                if not self.skip_string(char):
                    return None
            elif char == '`':
                if not self.skip_template_literal():
                    return None
            elif char == '/':
                if self.peek(1) in ['/', '*']:
                    if not self.skip_comment():
                        self.advance()
                elif not self.skip_regex_literal():
                    self.advance()
            elif char == '{':
                brace_count += 1
                self.advance()
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    return self.pos
                self.advance()
            else:
                self.advance()
        
        return None

def extract_functions_robust(file_path: str) -> List[Dict]:
    """健壮的函数提取器"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    functions = []
    parser = JSParser(content)
    
    # 支持 ES 模块的函数匹配模式
    patterns = [
        # export function functionName() { ... }
        (r'((?:export\s+)?function\s+(\w+)\s*\([^)]*\)\s*\{)', 'function_declaration'),
        # export const functionName = () => { ... }
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)', 'arrow_function_const'),
        # export const functionName = function() { ... }
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)', 'function_expression_const'),
        # let functionName = () => { ... }
        (r'(let\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)', 'arrow_function_let'),
        # var functionName = function() { ... }
        (r'(var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)', 'function_expression_var'),
        # export async function functionName() { ... }
        (r'((?:export\s+)?async\s+function\s+(\w+)\s*\([^)]*\)\s*\{)', 'async_function'),
        # const functionName = async () => { ... }
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*async\s*\([^)]*\)\s*=>\s*\{)', 'async_arrow_const'),
        # export default function() { ... } (匿名默认导出)
        (r'(export\s+default\s+function\s*\([^)]*\)\s*\{)', 'default_export_function'),
    ]
    
    for pattern, func_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            func_start = match.start()
            
            # 处理匿名默认导出
            if func_type == 'default_export_function':
                func_name = 'default_export'
            else:
                func_name = match.group(2)
            
            # 使用改进的括号匹配
            end_pos = parser.find_matching_brace(func_start)
            
            if end_pos is not None:
                func_content = content[func_start:end_pos + 1]
                
                # 过滤掉一些不合适的匹配
                if is_valid_function_robust(func_content, func_name, func_type, content, func_start):
                    functions.append({
                        'name': func_name,
                        'content': func_content,
                        'start_pos': func_start,
                        'end_pos': end_pos,
                        'type': func_type,
                        'size': len(func_content)
                    })
    
    return functions

def is_valid_function_robust(content: str, name: str, func_type: str, full_content: str, start_pos: int) -> bool:
    """改进的函数有效性验证"""
    # 过滤掉太短的内容
    if len(content) < 20:
        return False
    
    # 扩展的无效名称列表
    invalid_names = {
        'if', 'for', 'while', 'switch', 'catch', 'try', 'do', 'with',
        'forEach', 'map', 'filter', 'reduce', 'find', 'some', 'every',
        'then', 'catch', 'finally'  # Promise 方法
    }
    
    if name.lower() in invalid_names:
        return False
    
    # 注意：method 类型已被移除，此段代码为死代码，保留用于向后兼容
    # 在未来版本中可以安全删除
    
    return True

def extract_complex_objects_robust(file_path: str) -> List[Dict]:
    """健壮的复杂对象提取器"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    objects = []
    parser = JSParser(content)
    
    # 支持 ES 模块的对象匹配模式
    patterns = [
        # export const objectName = { ... }
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*\{)', 'object_const'),
        # let objectName = { ... }
        (r'(let\s+(\w+)\s*=\s*\{)', 'object_let'),
        # var objectName = { ... }
        (r'(var\s+(\w+)\s*=\s*\{)', 'object_var'),
    ]
    
    for pattern, obj_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            obj_start = match.start()
            obj_name = match.group(2)
            
            # 找到匹配的大括号
            end_pos = parser.find_matching_brace(obj_start)
            
            if end_pos is not None:
                obj_content = content[obj_start:end_pos + 1]
                
                # 只保存大型对象（超过500字符）
                if len(obj_content) > 500:
                    objects.append({
                        'name': obj_name,
                        'content': obj_content,
                        'start_pos': obj_start,
                        'end_pos': end_pos,
                        'type': obj_type,
                        'size': len(obj_content)
                    })
    
    return objects

def extract_global_constants_robust(file_path: str, all_items: List[Dict]) -> List[Dict]:
    """健壮的全局常量提取器"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    constants = []
    
    # 创建所有已提取项目的范围集合
    item_ranges = [(item['start_pos'], item['end_pos']) for item in all_items if 'end_pos' in item]
    
    # 支持 ES 模块的常量匹配模式
    patterns = [
        # export const CONSTANT = value;
        (r'((?:export\s+)?const\s+([A-Z_][A-Z0-9_]*)\s*=\s*[^{;]+;)', 'global_constant'),
        # const variableName = simpleValue;
        (r'((?:export\s+)?const\s+([a-z][a-zA-Z0-9_]*)\s*=\s*(?![{`])[^;]+;)', 'global_variable'),
        # const functionName = () => ... (简单箭头函数表达式)
        (r'((?:export\s+)?const\s+([a-z][a-zA-Z0-9_]*)\s*=\s*\([^)]*\)\s*=>\s*[^{][^;]*;)', 'arrow_expression'),
    ]
    
    for pattern, const_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        for match in matches:
            const_name = match.group(2)
            const_content = match.group(1).strip()
            match_pos = match.start()
            match_end = match.end()
            
            # 检查是否在任何已提取的项目内部
            is_inside_item = any(
                start <= match_pos <= end 
                for start, end in item_ranges
            )
            
            # 只保留全局常量（不在其他项目内部的）
            if not is_inside_item:
                # 过滤掉太长的定义（可能是复杂对象）和太短的定义
                if 10 < len(const_content) < 300:
                    constants.append({
                        'name': const_name,
                        'content': const_content,
                        'start_pos': match_pos,
                        'end_pos': match_end,
                        'type': const_type,
                        'size': len(const_content)
                    })
    
    return constants

def remove_nested_items(items: List[Dict]) -> List[Dict]:
    """移除嵌套项目，只保留最外层的项目"""
    if not items:
        return []
    
    # 按起始位置排序，如果起始位置相同则按结束位置降序排序
    items.sort(key=lambda x: (x['start_pos'], -x.get('end_pos', x['start_pos'])))
    
    unique_items = []
    
    for item in items:
        # 检查当前项目是否被任何已添加的项目包含
        is_nested = False
        
        for existing_item in unique_items:
            if (existing_item['start_pos'] <= item['start_pos'] and 
                existing_item.get('end_pos', existing_item['start_pos']) >= item.get('end_pos', item['start_pos'])):
                is_nested = True
                break
        
        if not is_nested:
            # 移除被当前项目包含的已有项目
            unique_items = [
                existing for existing in unique_items
                if not (item['start_pos'] <= existing['start_pos'] and 
                       item.get('end_pos', item['start_pos']) >= existing.get('end_pos', existing['start_pos']))
            ]
            unique_items.append(item)
    
    return unique_items

def remove_duplicates_by_name(items: List[Dict]) -> List[Dict]:
    """按名称去重"""
    seen_names = set()
    unique_items = []
    
    for item in items:
        if item['name'] not in seen_names:
            unique_items.append(item)
            seen_names.add(item['name'])
    
    return unique_items

def create_output_directories():
    """创建输出目录"""
    directories = [
        'extracted_functions_robust',
        'extracted_functions_robust/functions',
        'extracted_functions_robust/objects',
        'extracted_functions_robust/constants'
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

def save_items_to_files(items: List[Dict], output_dir: str, item_type: str):
    """将项目保存到独立文件"""
    for item in items:
        filename = f"{item['name']}.js"
        filepath = Path(output_dir) / filename
        
        # 智能处理 export 语句
        content_lines = item['content'].split('\n')
        has_export = any(line.strip().startswith('export') for line in content_lines)
        
        export_line = ""
        if not has_export:
            # 如果原内容没有 export，添加适当的 export 语句
            if item['name'] == 'default_export':
                # 对于默认导出，不添加额外的 export 语句
                # 因为内容已经包含了 export default
                export_line = ""
            else:
                export_line = f"\nexport {{ {item['name']} }};"
        # 如果已经有 export（包括 export default），保持原样
        
        content = f"""// {item_type}: {item['name']}
// 类型: {item.get('type', 'unknown')}
// 大小: {item['size']} 字符
// 从 js.js 自动提取

{item['content']}{export_line}"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已保存{item_type}: {item['name']} -> {filepath} ({item['size']} 字符)")

def generate_index_file(functions: List[Dict], objects: List[Dict], constants: List[Dict]):
    """生成索引文件，用于重新组装"""
    index_content = """// 自动生成的索引文件
// 用于导入所有拆分出来的模块

// 导入函数
"""
    
    # 收集所有导出的名称，处理默认导出的特殊情况
    exported_names = []
    
    for func in functions:
        if func['name'] == 'default_export':
            # 为默认导出使用更有意义的名称
            index_content += f"import defaultExportedFunction from './functions/{func['name']}.js';\n"
            exported_names.append('defaultExportedFunction')
        else:
            index_content += f"import {{ {func['name']} }} from './functions/{func['name']}.js';\n"
            exported_names.append(func['name'])
    
    index_content += "\n// 导入对象\n"
    for obj in objects:
        index_content += f"import {{ {obj['name']} }} from './objects/{obj['name']}.js';\n"
        exported_names.append(obj['name'])
    
    index_content += "\n// 导入常量\n"
    for const in constants:
        index_content += f"import {{ {const['name']} }} from './constants/{const['name']}.js';\n"
        exported_names.append(const['name'])
    
    # 生成导出语句
    if exported_names:
        index_content += "\n// 导出所有项目\nexport {\n"
        for i, name in enumerate(exported_names):
            comma = "," if i < len(exported_names) - 1 else ""
            index_content += f"  {name}{comma}\n"
        index_content += "};\n"
    
    # 添加使用说明
    index_content += """
// 使用示例:
// import { functionName, objectName, constantName } from './index.js';
// 或者导入默认导出的函数:
// import { defaultExportedFunction } from './index.js';
"""
    
    with open('extracted_functions_robust/index.js', 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    print("已生成索引文件: extracted_functions_robust/index.js")

def generate_robust_report(functions: List[Dict], objects: List[Dict], constants: List[Dict]):
    """生成健壮版本的拆分报告"""
    total_items = len(functions) + len(objects) + len(constants)
    
    report = {
        'extraction_info': {
            'total_functions': len(functions),
            'total_objects': len(objects),
            'total_constants': len(constants),
            'total_items': total_items,
            'improvements': [
                '支持正则表达式字面量解析',
                '改进的嵌套项目处理',
                '支持 ES 模块语法',
                '更严格的函数验证',
                '自动生成索引文件'
            ]
        },
        'functions': [
            {
                'name': f['name'], 
                'type': f['type'], 
                'size': f['size'],
                'start_pos': f['start_pos']
            } for f in functions
        ],
        'objects': [
            {
                'name': o['name'], 
                'type': o['type'], 
                'size': o['size'],
                'start_pos': o['start_pos']
            } for o in objects
        ],
        'constants': [
            {
                'name': c['name'], 
                'type': c['type'], 
                'size': c['size'],
                'start_pos': c['start_pos']
            } for c in constants
        ]
    }
    
    # 保存 JSON 报告
    with open('extracted_functions_robust/extraction_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 保存可读报告
    with open('extracted_functions_robust/extraction_report.md', 'w', encoding='utf-8') as f:
        f.write(f"""# JavaScript 代码拆分报告 (健壮版)

## 统计信息
- 提取的函数数量: {len(functions)}
- 提取的对象数量: {len(objects)}
- 提取的全局常量数量: {len(constants)}
- 总计: {total_items} 个项目

## 改进特性
- ✅ 支持正则表达式字面量解析
- ✅ 改进的嵌套项目处理
- ✅ 支持 ES 模块语法 (export/import)
- ✅ 更严格的函数验证逻辑
- ✅ 自动生成索引文件用于重新组装
- ✅ 更好的重复项和嵌套项处理

## 说明
- **函数**: 包括普通函数、箭头函数、异步函数等，支持 export 语法
- **对象**: 大型对象定义（如配置对象、HTML模板等）
- **全局常量**: 仅包括文件顶层的常量定义，不包括函数内部的局部常量

## 提取的函数列表
""")
        for i, func in enumerate(functions, 1):
            f.write(f"{i}. `{func['name']}()` - {func['type']} ({func['size']} 字符)\n")
        
        f.write(f"\n## 提取的对象列表\n")
        for i, obj in enumerate(objects, 1):
            f.write(f"{i}. `{obj['name']}` - {obj['type']} ({obj['size']} 字符)\n")
        
        f.write(f"\n## 提取的常量列表\n")
        for i, const in enumerate(constants, 1):
            f.write(f"{i}. `{const['name']}` - {const['type']} ({const['size']} 字符)\n")
        
        f.write(f"\n## 使用方法\n")
        f.write(f"1. 所有拆分的文件都包含适当的 export 语句\n")
        f.write(f"2. 使用 `index.js` 文件可以一次性导入所有模块\n")
        f.write(f"3. 示例: `import {{ functionName, objectName }} from './extracted_functions_robust/index.js'`\n")
    
    print(f"\n拆分完成！")
    print(f"- 提取了 {len(functions)} 个函数")
    print(f"- 提取了 {len(objects)} 个对象")
    print(f"- 提取了 {len(constants)} 个常量")
    print(f"- 报告已保存到 extracted_functions_robust/extraction_report.md")

def main():
    """主函数"""
    js_file_path = 'src/js/js.js'
    
    if not os.path.exists(js_file_path):
        print(f"错误: 找不到文件 {js_file_path}")
        return
    
    print(f"开始分析文件: {js_file_path}")
    print("使用健壮版解析器，支持正则表达式字面量和嵌套项处理...")
    
    # 创建输出目录
    create_output_directories()
    
    # 提取各种代码结构
    print("正在提取函数...")
    functions = extract_functions_robust(js_file_path)
    
    print("正在提取复杂对象...")
    objects = extract_complex_objects_robust(js_file_path)
    
    # 合并所有项目进行嵌套检查
    all_items = functions + objects
    
    print("正在提取全局常量...")
    constants = extract_global_constants_robust(js_file_path, all_items)
    
    # 添加常量到总列表
    all_items.extend(constants)
    
    print("正在处理嵌套项和重复项...")
    # 移除嵌套项
    all_items = remove_nested_items(all_items)
    
    # 重新分类
    functions = [item for item in all_items if item.get('type', '').endswith('function') or 'arrow' in item.get('type', '')]
    objects = [item for item in all_items if item.get('type', '').startswith('object')]
    constants = [item for item in all_items if item.get('type', '') in ['global_constant', 'global_variable', 'arrow_expression']]
    
    # 按名称去重
    functions = remove_duplicates_by_name(functions)
    objects = remove_duplicates_by_name(objects)
    constants = remove_duplicates_by_name(constants)
    
    # 保存到文件
    print("正在保存函数到文件...")
    save_items_to_files(functions, 'extracted_functions_robust/functions', '函数')
    
    print("正在保存对象到文件...")
    save_items_to_files(objects, 'extracted_functions_robust/objects', '对象')
    
    print("正在保存常量到文件...")
    save_items_to_files(constants, 'extracted_functions_robust/constants', '常量')
    
    # 生成索引文件
    print("正在生成索引文件...")
    generate_index_file(functions, objects, constants)
    
    # 生成报告
    generate_robust_report(functions, objects, constants)

if __name__ == '__main__':
    main()