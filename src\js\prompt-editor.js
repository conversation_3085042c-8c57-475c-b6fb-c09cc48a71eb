/**
 * 提示词模板编辑器
 * 负责模板的编辑、预览和保存功能
 */
class PromptEditor extends EventTarget {
    constructor(uiManager) {
        super();
        this.uiManager = uiManager;
        this.template = null;
        this.isEditMode = false;
        this.hasUnsavedChanges = false;
        
        // 界面元素
        this.element = null;
        this.templateNameInput = null;
        this.templateContentTextarea = null;
        this.variablesList = null;
        this.previewArea = null;
        
        this.createEditor();
    }

    /**
     * 创建编辑器界面
     */
    createEditor() {
        this.element = document.createElement('div');
        this.element.className = 'prompt-editor';

        this.element.innerHTML = `
            <div class="editor-header">
                <div class="editor-title">
                    <input type="text" id="template-name-input" class="template-name-input" placeholder="模板名称" readonly />
                    <span class="template-category-badge" id="template-category-badge"></span>
                </div>
                <div class="editor-actions">
                    <button id="edit-mode-btn" class="btn btn-primary">
                        <span class="icon">✏️</span> 编辑
                    </button>
                    <button id="save-template-btn" class="btn btn-success" style="display: none;">
                        <span class="icon">💾</span> 保存
                    </button>
                    <button id="cancel-edit-btn" class="btn btn-secondary" style="display: none;">
                        <span class="icon">❌</span> 取消
                    </button>
                    <button id="preview-template-btn" class="btn btn-info">
                        <span class="icon">👁️</span> 预览
                    </button>
                </div>
            </div>

            <div class="editor-body">
                <div class="editor-main">
                    <div class="template-content-section">
                        <h4>📝 模板内容</h4>
                        <div class="template-content-wrapper">
                            <textarea id="template-content" class="template-content-textarea" placeholder="在此输入模板内容..." readonly></textarea>
                            <div class="content-stats">
                                <span id="char-count">0 字符</span>
                                <span id="line-count">0 行</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-variables-section">
                        <h4>🔧 模板变量</h4>
                        <div id="variables-list" class="variables-list">
                            <div class="no-variables">暂无变量</div>
                        </div>
                        <div class="variables-actions" style="display: none;">
                            <button id="add-variable-btn" class="btn btn-small">
                                <span class="icon">➕</span> 添加变量
                            </button>
                        </div>
                    </div>
                </div>

                <div class="editor-sidebar">
                    <div class="template-info-section">
                        <h4>ℹ️ 模板信息</h4>
                        <div class="template-info">
                            <div class="info-item">
                                <label>分类：</label>
                                <select id="template-category-select" disabled>
                                    <option value="core">核心聊天模板</option>
                                    <option value="memory">记忆系统模板</option>
                                    <option value="moments">朋友圈模板</option>
                                    <option value="proactive">主动聊天模板</option>
                                    <option value="diary">日记模板</option>
                                    <option value="call">通话模板</option>
                                    <option value="media">媒体模板</option>
                                </select>
                            </div>
                            <div class="info-item">
                                <label>变量数量：</label>
                                <span id="variable-count">0</span>
                            </div>
                            <div class="info-item">
                                <label>字符数：</label>
                                <span id="character-count">0</span>
                            </div>
                            <div class="info-item">
                                <label>创建时间：</label>
                                <span id="created-time">-</span>
                            </div>
                            <div class="info-item">
                                <label>修改时间：</label>
                                <span id="modified-time">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="template-help-section">
                        <h4>💡 使用帮助</h4>
                        <div class="help-content">
                            <div class="help-item">
                                <strong>变量语法：</strong>
                                <code>{{variableName}}</code>
                            </div>
                            <div class="help-item">
                                <strong>嵌套属性：</strong>
                                <code>{{object.property}}</code>
                            </div>
                            <div class="help-item">
                                <strong>常用变量：</strong>
                                <ul>
                                    <li><code>{{character.realName}}</code> - 角色真名</li>
                                    <li><code>{{character.persona}}</code> - 角色人设</li>
                                    <li><code>{{userProfile.name}}</code> - 用户姓名</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="editor-preview" id="template-preview" style="display: none;">
                <div class="preview-header">
                    <h4>👁️ 模板预览</h4>
                    <button id="close-preview-btn" class="btn btn-small">关闭预览</button>
                </div>
                <div class="preview-content">
                    <div id="preview-result" class="preview-result"></div>
                </div>
            </div>
        `;

        // 获取关键元素引用
        this.templateNameInput = this.element.querySelector('#template-name-input');
        this.templateContentTextarea = this.element.querySelector('#template-content');
        this.variablesList = this.element.querySelector('#variables-list');
        this.previewArea = this.element.querySelector('#template-preview');
        this.categorySelect = this.element.querySelector('#template-category-select');
        this.categoryBadge = this.element.querySelector('#template-category-badge');

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 编辑模式按钮
        const editBtn = this.element.querySelector('#edit-mode-btn');
        editBtn?.addEventListener('click', () => this.enterEditMode());

        // 保存按钮
        const saveBtn = this.element.querySelector('#save-template-btn');
        saveBtn?.addEventListener('click', () => this.saveTemplate());

        // 取消编辑按钮
        const cancelBtn = this.element.querySelector('#cancel-edit-btn');
        cancelBtn?.addEventListener('click', () => this.cancelEdit());

        // 预览按钮
        const previewBtn = this.element.querySelector('#preview-template-btn');
        previewBtn?.addEventListener('click', () => this.togglePreview());

        // 关闭预览按钮
        const closePreviewBtn = this.element.querySelector('#close-preview-btn');
        closePreviewBtn?.addEventListener('click', () => this.hidePreview());

        // 添加变量按钮
        const addVariableBtn = this.element.querySelector('#add-variable-btn');
        addVariableBtn?.addEventListener('click', () => this.addVariable());

        // 模板内容变化监听
        this.templateContentTextarea?.addEventListener('input', () => {
            this.onContentChange();
        });

        // 模板名称变化监听
        this.templateNameInput?.addEventListener('input', () => {
            this.onNameChange();
        });

        // 分类选择变化监听
        this.categorySelect?.addEventListener('change', () => {
            this.onCategoryChange();
        });
    }

    /**
     * 加载模板到编辑器
     */
    loadTemplate(template) {
        this.template = template;
        this.hasUnsavedChanges = false;

        // 更新界面
        this.updateTemplateInfo();
        this.updateVariablesList();
        this.exitEditMode();
    }

    /**
     * 更新模板信息显示
     */
    updateTemplateInfo() {
        if (!this.template) return;

        // 更新模板名称
        this.templateNameInput.value = this.template.name || '';

        // 更新模板内容
        this.templateContentTextarea.value = this.template.template || '';

        // 更新分类
        this.categorySelect.value = this.template.category || 'core';
        this.categoryBadge.textContent = this.uiManager.promptManager.getCategories()[this.template.category] || this.template.category;
        this.categoryBadge.className = `template-category-badge category-${this.template.category}`;

        // 更新统计信息
        this.updateStats();

        // 更新时间信息
        const createdTime = this.template.createdAt ? new Date(this.template.createdAt).toLocaleString() : '-';
        const modifiedTime = this.template.modifiedAt ? new Date(this.template.modifiedAt).toLocaleString() : '-';
        
        this.element.querySelector('#created-time').textContent = createdTime;
        this.element.querySelector('#modified-time').textContent = modifiedTime;
    }

    /**
     * 更新变量列表
     */
    updateVariablesList() {
        if (!this.template || !this.template.variables || this.template.variables.length === 0) {
            this.variablesList.innerHTML = '<div class="no-variables">暂无变量</div>';
            return;
        }

        const variablesHTML = this.template.variables.map(variable => `
            <div class="variable-item">
                <span class="variable-name">{{${variable}}}</span>
                <div class="variable-actions">
                    <button class="btn-icon copy-variable" title="复制" data-variable="${variable}">📋</button>
                    ${this.isEditMode ? `<button class="btn-icon remove-variable" title="删除" data-variable="${variable}">🗑️</button>` : ''}
                </div>
            </div>
        `).join('');

        this.variablesList.innerHTML = variablesHTML;

        // 绑定变量操作事件
        this.bindVariableEvents();
    }

    /**
     * 绑定变量操作事件
     */
    bindVariableEvents() {
        this.variablesList.addEventListener('click', (e) => {
            if (e.target.classList.contains('copy-variable')) {
                const variable = e.target.dataset.variable;
                this.copyVariableToClipboard(variable);
            } else if (e.target.classList.contains('remove-variable')) {
                const variable = e.target.dataset.variable;
                this.removeVariable(variable);
            }
        });
    }

    /**
     * 复制变量到剪贴板
     */
    copyVariableToClipboard(variable) {
        const variableText = `{{${variable}}}`;
        navigator.clipboard.writeText(variableText).then(() => {
            this.showToast(`已复制变量: ${variableText}`);
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = variableText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast(`已复制变量: ${variableText}`);
        });
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const content = this.templateContentTextarea.value;
        const charCount = content.length;
        const lineCount = content.split('\n').length;

        // 更新字符和行数显示
        this.element.querySelector('#char-count').textContent = `${charCount} 字符`;
        this.element.querySelector('#line-count').textContent = `${lineCount} 行`;
        this.element.querySelector('#character-count').textContent = charCount;
        this.element.querySelector('#variable-count').textContent = this.template?.variables?.length || 0;
    }

    /**
     * 进入编辑模式
     */
    enterEditMode() {
        this.isEditMode = true;

        // 启用输入控件
        this.templateNameInput.readOnly = false;
        this.templateContentTextarea.readOnly = false;
        this.categorySelect.disabled = false;

        // 显示/隐藏按钮
        this.element.querySelector('#edit-mode-btn').style.display = 'none';
        this.element.querySelector('#save-template-btn').style.display = 'inline-block';
        this.element.querySelector('#cancel-edit-btn').style.display = 'inline-block';
        this.element.querySelector('.variables-actions').style.display = 'block';

        // 更新变量列表以显示删除按钮
        this.updateVariablesList();

        // 添加编辑模式样式
        this.element.classList.add('edit-mode');
    }

    /**
     * 退出编辑模式
     */
    exitEditMode() {
        this.isEditMode = false;

        // 禁用输入控件
        this.templateNameInput.readOnly = true;
        this.templateContentTextarea.readOnly = true;
        this.categorySelect.disabled = true;

        // 显示/隐藏按钮
        this.element.querySelector('#edit-mode-btn').style.display = 'inline-block';
        this.element.querySelector('#save-template-btn').style.display = 'none';
        this.element.querySelector('#cancel-edit-btn').style.display = 'none';
        this.element.querySelector('.variables-actions').style.display = 'none';

        // 更新变量列表以隐藏删除按钮
        this.updateVariablesList();

        // 移除编辑模式样式
        this.element.classList.remove('edit-mode');
    }

    /**
     * 保存模板
     */
    saveTemplate() {
        if (!this.template) return;

        // 获取当前编辑的内容
        const updatedTemplate = {
            ...this.template,
            name: this.templateNameInput.value.trim(),
            template: this.templateContentTextarea.value,
            category: this.categorySelect.value,
            modifiedAt: Date.now()
        };

        // 验证模板
        if (!this.validateTemplate(updatedTemplate)) {
            return;
        }

        // 保存到 PromptManager
        try {
            this.uiManager.promptManager.updateTemplate(this.template.id, updatedTemplate);
            this.template = updatedTemplate;
            this.hasUnsavedChanges = false;
            
            // 退出编辑模式
            this.exitEditMode();
            
            // 更新界面
            this.updateTemplateInfo();
            
            // 通知UI管理器更新列表
            this.uiManager.loadTemplateList();
            
            this.showToast('模板保存成功！');
            
            // 触发保存事件
            this.dispatchEvent(new CustomEvent('templateSaved', {
                detail: { templateId: this.template.id, template: updatedTemplate }
            }));
            
        } catch (error) {
            console.error('保存模板失败:', error);
            this.showToast('保存失败：' + error.message, 'error');
        }
    }

    /**
     * 验证模板
     */
    validateTemplate(template) {
        if (!template.name || template.name.trim() === '') {
            this.showToast('请输入模板名称', 'error');
            this.templateNameInput.focus();
            return false;
        }

        if (!template.template || template.template.trim() === '') {
            this.showToast('请输入模板内容', 'error');
            this.templateContentTextarea.focus();
            return false;
        }

        return true;
    }

    /**
     * 取消编辑
     */
    cancelEdit() {
        if (this.hasUnsavedChanges) {
            if (!confirm('有未保存的更改，确定要取消吗？')) {
                return;
            }
        }

        // 恢复原始内容
        this.updateTemplateInfo();
        this.exitEditMode();
        this.hasUnsavedChanges = false;
    }

    /**
     * 内容变化处理
     */
    onContentChange() {
        this.hasUnsavedChanges = true;
        this.updateStats();
        
        // 实时解析变量
        this.parseVariables();
    }

    /**
     * 名称变化处理
     */
    onNameChange() {
        this.hasUnsavedChanges = true;
    }

    /**
     * 分类变化处理
     */
    onCategoryChange() {
        this.hasUnsavedChanges = true;
        const category = this.categorySelect.value;
        this.categoryBadge.textContent = this.uiManager.promptManager.getCategories()[category] || category;
        this.categoryBadge.className = `template-category-badge category-${category}`;
    }

    /**
     * 解析模板中的变量
     */
    parseVariables() {
        const content = this.templateContentTextarea.value;
        const variableRegex = /\{\{([^}]+)\}\}/g;
        const variables = new Set();
        
        let match;
        while ((match = variableRegex.exec(content)) !== null) {
            variables.add(match[1].trim());
        }

        // 更新模板变量
        if (this.template) {
            this.template.variables = Array.from(variables);
            this.updateVariablesList();
            this.updateStats();
        }
    }

    /**
     * 切换预览
     */
    togglePreview() {
        if (this.previewArea.style.display === 'none') {
            this.showPreview();
        } else {
            this.hidePreview();
        }
    }

    /**
     * 显示预览
     */
    showPreview() {
        // 生成预览内容
        const previewContent = this.generatePreview();
        this.element.querySelector('#preview-result').innerHTML = previewContent;
        this.previewArea.style.display = 'block';
    }

    /**
     * 隐藏预览
     */
    hidePreview() {
        this.previewArea.style.display = 'none';
    }

    /**
     * 生成预览内容
     */
    generatePreview() {
        if (!this.template) return '<p>无模板内容</p>';

        const content = this.templateContentTextarea.value;
        
        // 简单的预览，将变量高亮显示
        const highlightedContent = content.replace(/\{\{([^}]+)\}\}/g, '<span class="variable-highlight">{{$1}}</span>');
        
        return `<pre class="preview-content">${highlightedContent}</pre>`;
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'success') {
        // 简单的提示实现，可以后续优化
        if (window.showToast) {
            window.showToast(message);
        } else {
            alert(message);
        }
    }

    /**
     * 获取编辑器元素
     */
    getElement() {
        return this.element;
    }

    /**
     * 添加变量
     */
    addVariable() {
        const variableName = prompt('请输入变量名称:');
        if (variableName && variableName.trim()) {
            const content = this.templateContentTextarea.value;
            const newContent = content + `{{${variableName.trim()}}}`;
            this.templateContentTextarea.value = newContent;
            this.onContentChange();
        }
    }

    /**
     * 移除变量
     */
    removeVariable(variable) {
        if (confirm(`确定要删除变量 {{${variable}}} 吗？`)) {
            const content = this.templateContentTextarea.value;
            const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g');
            const newContent = content.replace(regex, '');
            this.templateContentTextarea.value = newContent;
            this.onContentChange();
        }
    }
}

// 导出到全局作用域
window.PromptEditor = PromptEditor;
