<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>朋友圈模板系统集成测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-size: 12px; max-height: 300px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>朋友圈模板系统集成测试</h1>
    
    <div class="test-section">
        <h3>测试环境状态</h3>
        <div id="environment-status"></div>
    </div>
    
    <div class="test-section">
        <h3>朋友圈发布模板测试</h3>
        <button onclick="testMomentPost()">测试朋友圈发布</button>
        <div id="moment-post-result"></div>
    </div>
    
    <div class="test-section">
        <h3>朋友圈反应模板测试</h3>
        <button onclick="testMomentReaction()">测试朋友圈反应</button>
        <div id="moment-reaction-result"></div>
    </div>
    
    <div class="test-section">
        <h3>朋友圈回复评论测试</h3>
        <button onclick="testMomentReply()">测试回复评论</button>
        <div id="moment-reply-result"></div>
    </div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            },
            characters: [
                { 
                    id: 'char1', 
                    realName: '张小明',
                    remarkName: '小明', 
                    persona: '阳光开朗的大学生，喜欢运动和摄影'
                },
                { 
                    id: 'char2', 
                    realName: '李小红',
                    remarkName: '小红', 
                    persona: '温柔体贴的护士，喜欢烘焙和读书'
                }
            ],
            groups: [
                { 
                    id: 'group1', 
                    members: [
                        { originalCharId: 'char1' },
                        { originalCharId: 'char2' }
                    ]
                }
            ]
        };
        
        // 创建PromptManager实例
        let promptManager;
        
        // 检查环境状态
        function checkEnvironment() {
            const statusDiv = document.getElementById('environment-status');
            let status = '';
            
            try {
                promptManager = new PromptManager();
                promptManager.init();
                
                // 检查模板是否存在
                const momentPostTemplate = promptManager.getTemplate('momentPost');
                const momentReactionTemplate = promptManager.getTemplate('momentReaction');
                
                if (momentPostTemplate && momentReactionTemplate) {
                    status = `
                        <div class="success">
                            <h4>✅ 环境检查通过</h4>
                            <p>• PromptManager 初始化成功</p>
                            <p>• momentPost 模板已加载</p>
                            <p>• momentReaction 模板已加载</p>
                            <p>• 测试数据已准备</p>
                        </div>
                    `;
                } else {
                    status = `
                        <div class="warning">
                            <h4>⚠️ 模板缺失</h4>
                            <p>• momentPost 模板: ${momentPostTemplate ? '✅' : '❌'}</p>
                            <p>• momentReaction 模板: ${momentReactionTemplate ? '✅' : '❌'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                status = `
                    <div class="error">
                        <h4>❌ 环境检查失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
            
            statusDiv.innerHTML = status;
        }
        
        // 测试朋友圈发布模板
        function testMomentPost() {
            const resultDiv = document.getElementById('moment-post-result');
            
            try {
                const testCharacter = db.characters[0];
                const templateVariables = {
                    character: {
                        realName: testCharacter.realName,
                        persona: testCharacter.persona
                    }
                };
                
                const result = promptManager.renderPrompt('momentPost', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 朋友圈发布模板测试成功</h4>
                        <p><strong>测试角色:</strong> ${testCharacter.realName}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 朋友圈发布模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 测试朋友圈反应模板
        function testMomentReaction() {
            const resultDiv = document.getElementById('moment-reaction-result');
            
            try {
                const reactor = db.characters[1];
                const postAuthor = db.characters[0];
                const momentToReact = {
                    characterId: 'char1',
                    content: "今天天气真好，出门散步了！",
                    comments: []
                };
                
                const templateVariables = {
                    reactor: {
                        realName: reactor.realName,
                        persona: reactor.persona
                    },
                    postAuthor: postAuthor,
                    momentToReact: momentToReact,
                    friendshipStatus: `你和"${postAuthor.remarkName}"在同一个群聊里，你们是朋友。`,
                    existingComments: "",
                    taskInstructions: `现在，请决定你的行动。你有四个选择：
1. 点赞和评论: 如果你既想点赞也想评论，请回复格式：[LIKE][COMMENT:你的评论内容]
2. 只评论: 如果你只想评论，请回复格式：[COMMENT:你的评论内容]
3. 只点赞或忽略: 如果你只想点赞，或者觉得这条动态不需要互动，请只回复：[LIKE] 或 [IGNORE]
4. 删除动态(极小概率): 如果你扮演的正好是动态发布者(${postAuthor.remarkName})，并且觉得这条动态不合适，可以回复[DELETE]来删除它。
你的评论应该非常口语化、简洁，就像真实的朋友圈评论一样。请严格按照以上格式之一进行回复，不要添加任何额外的文字。`
                };
                
                const result = promptManager.renderPrompt('momentReaction', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 朋友圈反应模板测试成功</h4>
                        <p><strong>反应者:</strong> ${reactor.realName}</p>
                        <p><strong>动态作者:</strong> ${postAuthor.remarkName}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 朋友圈反应模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 测试回复评论模板
        function testMomentReply() {
            const resultDiv = document.getElementById('moment-reply-result');
            
            try {
                const reactor = db.characters[1];
                const postAuthor = db.characters[0];
                const momentToReact = {
                    characterId: 'char1',
                    content: "今天天气真好，出门散步了！",
                    comments: [
                        {
                            characterId: 'user_me',
                            content: "确实很不错呢！"
                        }
                    ]
                };
                
                const templateVariables = {
                    reactor: {
                        realName: reactor.realName,
                        persona: reactor.persona
                    },
                    postAuthor: postAuthor,
                    momentToReact: momentToReact,
                    friendshipStatus: `你和"${postAuthor.remarkName}"在同一个群聊里，你们是朋友。`,
                    existingComments: `
--- 已有评论 ---
我: 确实很不错呢！
`,
                    taskInstructions: `现在，请你作为"${reactor.realName}"，回复"我"的评论："确实很不错呢！"。
请直接输出你的回复内容，不要带任何前缀或格式。`
                };
                
                const result = promptManager.renderPrompt('momentReaction', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 朋友圈回复评论模板测试成功</h4>
                        <p><strong>回复者:</strong> ${reactor.realName}</p>
                        <p><strong>回复目标:</strong> 我的评论</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 朋友圈回复评论模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 100);
        });
    </script>
</body>
</html>
