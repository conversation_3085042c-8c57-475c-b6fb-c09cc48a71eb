#!/usr/bin/env python3
"""
JavaScript 函数拆分脚本 - 改进版
将 js.js 文件中的所有函数拆分为独立的文件
支持更复杂的 JavaScript 语法结构
"""

import re
import os
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class JSParser:
    def __init__(self, content: str):
        self.content = content
        self.length = len(content)
        self.pos = 0
        
    def peek(self, offset: int = 0) -> str:
        """查看指定位置的字符"""
        pos = self.pos + offset
        return self.content[pos] if pos < self.length else ''
    
    def advance(self, count: int = 1) -> str:
        """前进指定字符数并返回当前字符"""
        char = self.peek()
        self.pos += count
        return char
    
    def skip_whitespace(self):
        """跳过空白字符"""
        while self.pos < self.length and self.content[self.pos].isspace():
            self.pos += 1
    
    def skip_string(self, quote_char: str) -> bool:
        """跳过字符串内容，返回是否成功"""
        if self.peek() != quote_char:
            return False
            
        self.advance()  # 跳过开始引号
        
        while self.pos < self.length:
            char = self.peek()
            if char == '\\':
                self.advance(2)  # 跳过转义字符
            elif char == quote_char:
                self.advance()  # 跳过结束引号
                return True
            else:
                self.advance()
        
        return False  # 未找到结束引号
    
    def skip_template_literal(self) -> bool:
        """跳过模板字符串（反引号）"""
        if self.peek() != '`':
            return False
            
        self.advance()  # 跳过开始反引号
        
        while self.pos < self.length:
            char = self.peek()
            if char == '\\':
                self.advance(2)  # 跳过转义字符
            elif char == '$' and self.peek(1) == '{':
                # 处理模板表达式 ${...}
                self.advance(2)  # 跳过 ${
                brace_count = 1
                while self.pos < self.length and brace_count > 0:
                    char = self.peek()
                    if char in ['"', "'", '`']:
                        if not self.skip_string(char):
                            return False
                    elif char == '{':
                        brace_count += 1
                        self.advance()
                    elif char == '}':
                        brace_count -= 1
                        self.advance()
                    else:
                        self.advance()
            elif char == '`':
                self.advance()  # 跳过结束反引号
                return True
            else:
                self.advance()
        
        return False  # 未找到结束反引号
    
    def skip_comment(self) -> bool:
        """跳过注释"""
        if self.peek() == '/' and self.peek(1) == '/':
            # 单行注释
            while self.pos < self.length and self.peek() != '\n':
                self.advance()
            return True
        elif self.peek() == '/' and self.peek(1) == '*':
            # 多行注释
            self.advance(2)
            while self.pos < self.length - 1:
                if self.peek() == '*' and self.peek(1) == '/':
                    self.advance(2)
                    return True
                self.advance()
        return False
    
    def find_matching_brace(self, start_pos: int) -> Optional[int]:
        """找到匹配的大括号位置"""
        self.pos = start_pos
        
        # 找到第一个开括号
        while self.pos < self.length and self.peek() != '{':
            self.advance()
        
        if self.pos >= self.length:
            return None
            
        brace_count = 1
        self.advance()  # 跳过第一个 {
        
        while self.pos < self.length and brace_count > 0:
            char = self.peek()
            
            if char in ['"', "'"]:
                if not self.skip_string(char):
                    return None
            elif char == '`':
                if not self.skip_template_literal():
                    return None
            elif char == '/' and (self.peek(1) == '/' or self.peek(1) == '*'):
                if not self.skip_comment():
                    self.advance()
            elif char == '{':
                brace_count += 1
                self.advance()
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    return self.pos
                self.advance()
            else:
                self.advance()
        
        return None

def extract_functions_improved(file_path: str) -> List[Dict]:
    """改进的函数提取器"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    functions = []
    parser = JSParser(content)
    
    # 更全面的函数匹配模式
    patterns = [
        # function functionName() { ... }
        (r'(function\s+(\w+)\s*\([^)]*\)\s*\{)', 'function_declaration'),
        # const functionName = () => { ... }
        (r'(const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)', 'arrow_function_const'),
        # const functionName = function() { ... }
        (r'(const\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)', 'function_expression_const'),
        # let functionName = () => { ... }
        (r'(let\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)', 'arrow_function_let'),
        # var functionName = function() { ... }
        (r'(var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)', 'function_expression_var'),
        # async function functionName() { ... }
        (r'(async\s+function\s+(\w+)\s*\([^)]*\)\s*\{)', 'async_function'),
        # async const functionName = () => { ... }
        (r'(const\s+(\w+)\s*=\s*async\s*\([^)]*\)\s*=>\s*\{)', 'async_arrow_const'),
        # methodName() { ... } (对象方法)
        (r'(\b(\w+)\s*\([^)]*\)\s*\{)', 'method'),
    ]
    
    for pattern, func_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            func_start = match.start()
            func_name = match.group(2)
            
            # 使用改进的括号匹配
            end_pos = parser.find_matching_brace(func_start)
            
            if end_pos is not None:
                func_content = content[func_start:end_pos + 1]
                
                # 过滤掉一些不合适的匹配
                if is_valid_function(func_content, func_name, func_type):
                    functions.append({
                        'name': func_name,
                        'content': func_content,
                        'start_pos': func_start,
                        'end_pos': end_pos,
                        'type': func_type,
                        'size': len(func_content)
                    })
    
    # 去重并排序
    functions.sort(key=lambda x: x['start_pos'])
    unique_functions = remove_duplicates(functions)
    
    return unique_functions

def is_valid_function(content: str, name: str, func_type: str) -> bool:
    """验证是否为有效的函数定义"""
    # 过滤掉太短的内容
    if len(content) < 20:
        return False
    
    # 过滤掉一些常见的误匹配
    invalid_names = {'if', 'for', 'while', 'switch', 'catch', 'try'}
    if name.lower() in invalid_names:
        return False
    
    # 对象方法需要额外验证
    if func_type == 'method':
        # 检查是否在对象字面量中
        lines_before = content[:50].split('\n')
        if any(':' in line for line in lines_before):
            return True
        return False
    
    return True

def extract_complex_objects(file_path: str) -> List[Dict]:
    """提取复杂对象定义（如 htmlMap）"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    objects = []
    parser = JSParser(content)
    
    # 匹配对象定义
    patterns = [
        # const objectName = { ... }
        (r'(const\s+(\w+)\s*=\s*\{)', 'object_const'),
        # let objectName = { ... }
        (r'(let\s+(\w+)\s*=\s*\{)', 'object_let'),
        # var objectName = { ... }
        (r'(var\s+(\w+)\s*=\s*\{)', 'object_var'),
    ]
    
    for pattern, obj_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            obj_start = match.start()
            obj_name = match.group(2)
            
            # 找到匹配的大括号
            end_pos = parser.find_matching_brace(obj_start)
            
            if end_pos is not None:
                obj_content = content[obj_start:end_pos + 1]
                
                # 只保存大型对象（超过500字符）
                if len(obj_content) > 500:
                    objects.append({
                        'name': obj_name,
                        'content': obj_content,
                        'start_pos': obj_start,
                        'end_pos': end_pos,
                        'type': obj_type,
                        'size': len(obj_content)
                    })
    
    # 去重并排序
    objects.sort(key=lambda x: x['start_pos'])
    unique_objects = remove_duplicates(objects)
    
    return unique_objects

def extract_global_constants(file_path: str, functions: List[Dict]) -> List[Dict]:
    """提取全局常量定义（不包括函数内部的常量）"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    constants = []
    
    # 创建函数范围集合，用于排除函数内的常量
    function_ranges = [(f['start_pos'], f['end_pos']) for f in functions if 'end_pos' in f]
    
    # 匹配简单的常量定义（排除复杂对象）
    patterns = [
        # const CONSTANT = value;
        (r'(const\s+([A-Z_][A-Z0-9_]*)\s*=\s*[^{;]+;)', 'global_constant'),
        # const variableName = simpleValue;
        (r'(const\s+([a-z][a-zA-Z0-9_]*)\s*=\s*(?![{`])[^;]+;)', 'global_variable'),
        # const functionName = () => ... (简单箭头函数，但不在函数内)
        (r'(const\s+([a-z][a-zA-Z0-9_]*)\s*=\s*\([^)]*\)\s*=>\s*[^{][^;]*;)', 'arrow_expression'),
    ]
    
    for pattern, const_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        for match in matches:
            const_name = match.group(2)
            const_content = match.group(1).strip()
            match_pos = match.start()
            
            # 检查是否在任何函数内部
            is_inside_function = any(
                start <= match_pos <= end 
                for start, end in function_ranges
            )
            
            # 只保留全局常量（不在函数内部的）
            if not is_inside_function:
                # 过滤掉太长的定义（可能是复杂对象）和太短的定义
                if 10 < len(const_content) < 200:
                    constants.append({
                        'name': const_name,
                        'content': const_content,
                        'start_pos': match_pos,
                        'type': const_type,
                        'size': len(const_content)
                    })
    
    # 去重并排序
    constants.sort(key=lambda x: x['start_pos'])
    unique_constants = remove_duplicates(constants)
    
    return unique_constants

def remove_duplicates(items: List[Dict]) -> List[Dict]:
    """去除重复项"""
    seen_names = set()
    unique_items = []
    
    for item in items:
        if item['name'] not in seen_names:
            unique_items.append(item)
            seen_names.add(item['name'])
    
    return unique_items

def create_output_directories():
    """创建输出目录"""
    directories = [
        'extracted_functions_improved',
        'extracted_functions_improved/functions',
        'extracted_functions_improved/objects',
        'extracted_functions_improved/constants'
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

def save_items_to_files(items: List[Dict], output_dir: str, item_type: str):
    """将项目保存到独立文件"""
    for item in items:
        filename = f"{item['name']}.js"
        filepath = Path(output_dir) / filename
        
        content = f"""// {item_type}: {item['name']}
// 类型: {item.get('type', 'unknown')}
// 大小: {item['size']} 字符
// 从 js.js 自动提取

{item['content']}
"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已保存{item_type}: {item['name']} -> {filepath} ({item['size']} 字符)")

def generate_improved_report(functions: List[Dict], objects: List[Dict], constants: List[Dict]):
    """生成改进的拆分报告"""
    total_items = len(functions) + len(objects) + len(constants)
    
    report = {
        'extraction_info': {
            'total_functions': len(functions),
            'total_objects': len(objects),
            'total_constants': len(constants),
            'total_items': total_items
        },
        'functions': [
            {
                'name': f['name'], 
                'type': f['type'], 
                'size': f['size'],
                'start_pos': f['start_pos']
            } for f in functions
        ],
        'objects': [
            {
                'name': o['name'], 
                'type': o['type'], 
                'size': o['size'],
                'start_pos': o['start_pos']
            } for o in objects
        ],
        'constants': [
            {
                'name': c['name'], 
                'type': c['type'], 
                'size': c['size'],
                'start_pos': c['start_pos']
            } for c in constants
        ]
    }
    
    # 保存 JSON 报告
    with open('extracted_functions_improved/extraction_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 保存可读报告
    with open('extracted_functions_improved/extraction_report.md', 'w', encoding='utf-8') as f:
        f.write(f"""# JavaScript 代码拆分报告 (改进版)

## 统计信息
- 提取的函数数量: {len(functions)}
- 提取的对象数量: {len(objects)}
- 提取的全局常量数量: {len(constants)}
- 总计: {total_items} 个项目

## 说明
- **函数**: 包括普通函数、箭头函数、异步函数等
- **对象**: 大型对象定义（如配置对象、HTML模板等）
- **全局常量**: 仅包括文件顶层的常量定义，不包括函数内部的局部常量

## 提取的函数列表
""")
        for i, func in enumerate(functions, 1):
            f.write(f"{i}. `{func['name']}()` - {func['type']} ({func['size']} 字符)\n")
        
        f.write(f"\n## 提取的对象列表\n")
        for i, obj in enumerate(objects, 1):
            f.write(f"{i}. `{obj['name']}` - {obj['type']} ({obj['size']} 字符)\n")
        
        f.write(f"\n## 提取的常量列表\n")
        for i, const in enumerate(constants, 1):
            f.write(f"{i}. `{const['name']}` - {const['type']} ({const['size']} 字符)\n")
    
    print(f"\n拆分完成！")
    print(f"- 提取了 {len(functions)} 个函数")
    print(f"- 提取了 {len(objects)} 个对象")
    print(f"- 提取了 {len(constants)} 个常量")
    print(f"- 报告已保存到 extracted_functions_improved/extraction_report.md")

def main():
    """主函数"""
    js_file_path = 'src/js/js.js'
    
    if not os.path.exists(js_file_path):
        print(f"错误: 找不到文件 {js_file_path}")
        return
    
    print(f"开始分析文件: {js_file_path}")
    
    # 创建输出目录
    create_output_directories()
    
    # 提取各种代码结构
    print("正在提取函数...")
    functions = extract_functions_improved(js_file_path)
    
    print("正在提取复杂对象...")
    objects = extract_complex_objects(js_file_path)
    
    print("正在提取全局常量...")
    constants = extract_global_constants(js_file_path, functions)
    
    # 保存到文件
    print("正在保存函数到文件...")
    save_items_to_files(functions, 'extracted_functions_improved/functions', '函数')
    
    print("正在保存对象到文件...")
    save_items_to_files(objects, 'extracted_functions_improved/objects', '对象')
    
    print("正在保存常量到文件...")
    save_items_to_files(constants, 'extracted_functions_improved/constants', '常量')
    
    # 生成报告
    generate_improved_report(functions, objects, constants)

if __name__ == '__main__':
    main()