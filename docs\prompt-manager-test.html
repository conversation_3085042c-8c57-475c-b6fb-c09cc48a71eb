<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PromptManager测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧠 PromptManager 测试页面</h1>
    
    <div class="test-section">
        <h2>1. 基础功能测试</h2>
        <button onclick="testBasicFunctions()">运行基础测试</button>
        <div id="basic-results"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 模板渲染测试</h2>
        <button onclick="testTemplateRendering()">测试模板渲染</button>
        <div id="template-results"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 记忆系统测试</h2>
        <button onclick="testMemorySystem()">测试记忆系统</button>
        <div id="memory-results"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 自定义模板测试</h2>
        <textarea id="custom-template" placeholder="输入自定义模板，使用 {{variable}} 语法...">你好 {{user}}，我是 {{char}}。今天是 {{date}}。</textarea>
        <br>
        <input type="text" id="template-vars" placeholder="变量 (JSON格式): {&quot;user&quot;:&quot;小明&quot;,&quot;char&quot;:&quot;AI助手&quot;,&quot;date&quot;:&quot;2024年&quot;}" style="width: 100%; padding: 5px;">
        <br>
        <button onclick="testCustomTemplate()">测试自定义模板</button>
        <div id="custom-results"></div>
    </div>

    <script>
        // 模拟必要的全局变量和函数
        window.db = {
            promptCategories: null,
            promptTemplates: null,
            memorySettings: {
                injectionPrompt: '这是测试的记忆注入提示词：{{memories}}',
                extractionPrompt: '这是测试的记忆提取提示词，历史：{{history}}'
            }
        };
        
        window.saveData = function() {
            console.log('模拟保存数据到localStorage');
        };
        
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        function testBasicFunctions() {
            clearResults('basic-results');
            
            try {
                // 测试PromptManager是否存在
                if (typeof PromptManager === 'undefined') {
                    addResult('basic-results', '❌ PromptManager类未找到', 'error');
                    return;
                }
                addResult('basic-results', '✅ PromptManager类已加载', 'success');
                
                // 测试实例化
                const pm = new PromptManager();
                addResult('basic-results', '✅ PromptManager实例创建成功', 'success');
                
                // 测试初始化
                pm.init();
                addResult('basic-results', '✅ PromptManager初始化成功', 'success');
                
                // 测试分类获取
                const categories = pm.getCategories();
                addResult('basic-results', `✅ 获取到 ${Object.keys(categories).length} 个分类`, 'success');
                
                // 测试模板获取
                const templates = pm.getAllTemplates();
                addResult('basic-results', `✅ 获取到 ${Object.keys(templates).length} 个模板`, 'success');
                
            } catch (error) {
                addResult('basic-results', `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        function testTemplateRendering() {
            clearResults('template-results');
            
            try {
                const pm = new PromptManager();
                pm.init();
                
                // 测试记忆注入模板
                const injectionResult = pm.renderPrompt('memoryInjection', {
                    char: '测试角色',
                    user: '测试用户',
                    memories: '这是一些测试记忆内容'
                });
                
                if (injectionResult && injectionResult.includes('测试角色')) {
                    addResult('template-results', '✅ 记忆注入模板渲染成功', 'success');
                    addResult('template-results', `渲染结果预览: ${injectionResult.substring(0, 100)}...`, 'info');
                } else {
                    addResult('template-results', '❌ 记忆注入模板渲染失败', 'error');
                }
                
                // 测试记忆提取模板
                const extractionResult = pm.renderPrompt('memoryExtraction', {
                    user: '测试用户',
                    charIfNotGroup: '测试角色',
                    memories: '已有记忆',
                    history: '对话历史'
                });
                
                if (extractionResult && extractionResult.includes('测试用户')) {
                    addResult('template-results', '✅ 记忆提取模板渲染成功', 'success');
                } else {
                    addResult('template-results', '❌ 记忆提取模板渲染失败', 'error');
                }
                
            } catch (error) {
                addResult('template-results', `❌ 模板渲染测试失败: ${error.message}`, 'error');
            }
        }
        
        function testMemorySystem() {
            clearResults('memory-results');
            
            try {
                const pm = new PromptManager();
                pm.init();
                
                // 测试降级机制
                const fallbackInjection = pm.getFallbackPrompt('memoryInjection', {});
                if (fallbackInjection) {
                    addResult('memory-results', '✅ 记忆注入降级机制正常', 'success');
                } else {
                    addResult('memory-results', '❌ 记忆注入降级机制失败', 'error');
                }
                
                const fallbackExtraction = pm.getFallbackPrompt('memoryExtraction', {});
                if (fallbackExtraction) {
                    addResult('memory-results', '✅ 记忆提取降级机制正常', 'success');
                } else {
                    addResult('memory-results', '❌ 记忆提取降级机制失败', 'error');
                }
                
                // 测试模板验证
                const validation = pm.validateTemplateVariables('memoryInjection', {
                    char: '角色',
                    user: '用户',
                    memories: '记忆'
                });
                
                if (validation.valid) {
                    addResult('memory-results', '✅ 模板变量验证通过', 'success');
                } else {
                    addResult('memory-results', `❌ 模板变量验证失败: ${validation.error}`, 'error');
                }
                
            } catch (error) {
                addResult('memory-results', `❌ 记忆系统测试失败: ${error.message}`, 'error');
            }
        }
        
        function testCustomTemplate() {
            clearResults('custom-results');
            
            try {
                const template = document.getElementById('custom-template').value;
                const varsInput = document.getElementById('template-vars').value;
                
                if (!template) {
                    addResult('custom-results', '❌ 请输入模板内容', 'error');
                    return;
                }
                
                let variables = {};
                if (varsInput) {
                    try {
                        variables = JSON.parse(varsInput);
                    } catch (e) {
                        addResult('custom-results', '❌ 变量JSON格式错误', 'error');
                        return;
                    }
                }
                
                // 手动渲染模板
                let rendered = template;
                for (const [key, value] of Object.entries(variables)) {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    rendered = rendered.replace(regex, value || '');
                }
                
                addResult('custom-results', '✅ 自定义模板渲染成功', 'success');
                addResult('custom-results', `渲染结果: ${rendered}`, 'info');
                
            } catch (error) {
                addResult('custom-results', `❌ 自定义模板测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('basic-results', '🔄 页面加载完成，开始自动测试...', 'info');
                testBasicFunctions();
            }, 1000);
        });
    </script>
    
    <!-- 加载PromptManager -->
    <script src="../src/js/prompt-manager.js"></script>
</body>
</html>
