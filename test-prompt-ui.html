<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词管理界面测试</title>
    <link rel="stylesheet" href="./src/css/prompt-ui.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .test-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .test-status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .status-label {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .status-value {
            color: #7f8c8d;
        }
        
        .status-value.success {
            color: #27ae60;
        }
        
        .status-value.error {
            color: #e74c3c;
        }
        
        .test-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 提示词管理界面测试</h1>
            <p>测试提示词管理系统的用户界面功能</p>
        </div>

        <div class="test-status" id="test-status">
            <div class="status-item">
                <span class="status-label">PromptManager状态:</span>
                <span class="status-value" id="prompt-manager-status">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">PromptUIManager状态:</span>
                <span class="status-value" id="prompt-ui-manager-status">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">PromptEditor状态:</span>
                <span class="status-value" id="prompt-editor-status">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">模板总数:</span>
                <span class="status-value" id="template-count">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">界面状态:</span>
                <span class="status-value" id="ui-status">未打开</span>
            </div>
        </div>

        <div class="test-actions">
            <button class="btn btn-primary" onclick="initializeSystem()">
                🚀 初始化系统
            </button>
            <button class="btn btn-success" onclick="openPromptUI()">
                🎯 打开提示词管理界面
            </button>
            <button class="btn btn-warning" onclick="testTemplateOperations()">
                🧪 测试模板操作
            </button>
            <button class="btn btn-danger" onclick="clearLog()">
                🗑️ 清空日志
            </button>
        </div>

        <div class="test-log" id="test-log">
=== 提示词管理界面测试日志 ===
等待初始化...

</div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="./src/js/prompt-manager.js"></script>
    <script src="./src/js/prompt-ui-manager.js"></script>
    <script src="./src/js/prompt-editor.js"></script>

    <script>
        // 全局变量
        let promptManager = null;
        let promptUIManager = null;
        let testLog = '';

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}`;
            
            testLog += logMessage + '\n';
            document.getElementById('test-log').textContent = testLog;
            
            // 自动滚动到底部
            const logElement = document.getElementById('test-log');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logMessage);
        }

        // 更新状态显示
        function updateStatus() {
            // 检查PromptManager
            const pmStatus = document.getElementById('prompt-manager-status');
            if (typeof PromptManager !== 'undefined' && promptManager) {
                pmStatus.textContent = '已加载并初始化';
                pmStatus.className = 'status-value success';
            } else if (typeof PromptManager !== 'undefined') {
                pmStatus.textContent = '已加载，未初始化';
                pmStatus.className = 'status-value';
            } else {
                pmStatus.textContent = '未加载';
                pmStatus.className = 'status-value error';
            }

            // 检查PromptUIManager
            const puiStatus = document.getElementById('prompt-ui-manager-status');
            if (typeof PromptUIManager !== 'undefined' && promptUIManager) {
                puiStatus.textContent = '已加载并初始化';
                puiStatus.className = 'status-value success';
            } else if (typeof PromptUIManager !== 'undefined') {
                puiStatus.textContent = '已加载，未初始化';
                puiStatus.className = 'status-value';
            } else {
                puiStatus.textContent = '未加载';
                puiStatus.className = 'status-value error';
            }

            // 检查PromptEditor
            const peStatus = document.getElementById('prompt-editor-status');
            if (typeof PromptEditor !== 'undefined') {
                peStatus.textContent = '已加载';
                peStatus.className = 'status-value success';
            } else {
                peStatus.textContent = '未加载';
                peStatus.className = 'status-value error';
            }

            // 更新模板数量
            const templateCount = document.getElementById('template-count');
            if (promptManager) {
                templateCount.textContent = promptManager.getTemplateCount();
            } else {
                templateCount.textContent = '-';
            }

            // 更新界面状态
            const uiStatus = document.getElementById('ui-status');
            if (promptUIManager && promptUIManager.isVisible()) {
                uiStatus.textContent = '已打开';
                uiStatus.className = 'status-value success';
            } else {
                uiStatus.textContent = '未打开';
                uiStatus.className = 'status-value';
            }
        }

        // 初始化系统
        function initializeSystem() {
            log('开始初始化提示词管理系统...');
            
            try {
                // 初始化PromptManager
                if (typeof PromptManager !== 'undefined') {
                    promptManager = new PromptManager();
                    promptManager.init();
                    log('PromptManager初始化成功', 'success');
                } else {
                    log('PromptManager类未找到', 'error');
                    return;
                }

                // 初始化PromptUIManager
                if (typeof PromptUIManager !== 'undefined') {
                    promptUIManager = new PromptUIManager(promptManager);
                    promptUIManager.init();
                    log('PromptUIManager初始化成功', 'success');
                } else {
                    log('PromptUIManager类未找到', 'error');
                    return;
                }

                log('系统初始化完成！', 'success');
                updateStatus();
                
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                console.error(error);
            }
        }

        // 打开提示词管理界面
        function openPromptUI() {
            if (!promptUIManager) {
                log('请先初始化系统', 'warning');
                return;
            }

            try {
                promptUIManager.show();
                log('提示词管理界面已打开', 'success');
                updateStatus();
            } catch (error) {
                log(`打开界面失败: ${error.message}`, 'error');
            }
        }

        // 测试模板操作
        function testTemplateOperations() {
            if (!promptManager) {
                log('请先初始化系统', 'warning');
                return;
            }

            try {
                log('开始测试模板操作...');

                // 测试获取所有模板
                const allTemplates = promptManager.getAllTemplates();
                log(`获取到 ${Object.keys(allTemplates).length} 个模板`);

                // 测试按分类获取模板
                const coreTemplates = promptManager.getTemplatesByCategory('core');
                log(`核心分类有 ${Object.keys(coreTemplates).length} 个模板`);

                // 测试创建自定义模板
                const testTemplate = {
                    name: '测试模板',
                    template: '这是一个测试模板，包含变量：{{testVariable}}',
                    category: 'core',
                    variables: ['testVariable']
                };

                const newTemplateId = promptManager.addTemplate(testTemplate);
                log(`创建测试模板成功，ID: ${newTemplateId}`, 'success');

                // 测试更新模板
                const updatedTemplate = {
                    ...testTemplate,
                    name: '更新后的测试模板',
                    template: '这是更新后的模板内容：{{testVariable}}'
                };

                promptManager.updateTemplate(newTemplateId, updatedTemplate);
                log('模板更新成功', 'success');

                // 测试复制模板
                const duplicatedId = promptManager.duplicateTemplate(newTemplateId);
                log(`模板复制成功，新ID: ${duplicatedId}`, 'success');

                // 测试删除模板
                promptManager.deleteTemplate(newTemplateId);
                promptManager.deleteTemplate(duplicatedId);
                log('测试模板删除成功', 'success');

                log('模板操作测试完成！', 'success');
                updateStatus();

            } catch (error) {
                log(`模板操作测试失败: ${error.message}`, 'error');
                console.error(error);
            }
        }

        // 清空日志
        function clearLog() {
            testLog = '=== 提示词管理界面测试日志 ===\n日志已清空\n\n';
            document.getElementById('test-log').textContent = testLog;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('测试页面加载完成');
            log('请点击"初始化系统"按钮开始测试');
            updateStatus();
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
</body>
</html>
