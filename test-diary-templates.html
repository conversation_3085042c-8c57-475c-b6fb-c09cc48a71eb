<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日记模板系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-size: 12px; max-height: 400px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>日记模板系统测试</h1>
    
    <div class="test-section">
        <h3>测试环境状态</h3>
        <div id="environment-status"></div>
    </div>
    
    <div class="test-section">
        <h3>日记撰写模板测试</h3>
        <button onclick="testDiaryEntry()">测试日记撰写</button>
        <div id="diary-entry-result"></div>
    </div>
    
    <div class="test-section">
        <h3>日记模板对比测试</h3>
        <button onclick="testDiaryComparison()">对比原始函数</button>
        <div id="diary-comparison-result"></div>
    </div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            },
            userProfiles: [
                {
                    id: 'user1',
                    name: '张三'
                }
            ],
            characters: [
                { 
                    id: 'char1', 
                    realName: '李小红',
                    remarkName: '小红', 
                    persona: '温柔体贴的护士，喜欢烘焙和读书，内心细腻敏感',
                    userProfileId: 'user1'
                }
            ]
        };
        
        // 创建PromptManager实例
        let promptManager;
        
        // 检查环境状态
        function checkEnvironment() {
            const statusDiv = document.getElementById('environment-status');
            let status = '';
            
            try {
                promptManager = new PromptManager();
                promptManager.init();
                
                // 检查模板是否存在
                const diaryEntryTemplate = promptManager.getTemplate('diaryEntry');
                
                if (diaryEntryTemplate) {
                    status = `
                        <div class="success">
                            <h4>✅ 环境检查通过</h4>
                            <p>• PromptManager 初始化成功</p>
                            <p>• diaryEntry 模板已加载</p>
                            <p>• 测试数据已准备</p>
                        </div>
                    `;
                } else {
                    status = `
                        <div class="warning">
                            <h4>⚠️ 模板缺失</h4>
                            <p>• diaryEntry 模板: ${diaryEntryTemplate ? '✅' : '❌'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                status = `
                    <div class="error">
                        <h4>❌ 环境检查失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
            
            statusDiv.innerHTML = status;
        }
        
        // 测试日记撰写模板
        function testDiaryEntry() {
            const resultDiv = document.getElementById('diary-entry-result');
            
            try {
                const testCharacter = db.characters[0];
                const userProfile = db.userProfiles[0];
                
                // 模拟聊天记录
                const recentHistory = `我: 今天工作怎么样？
TA: 还不错，今天病人都很配合
我: 那就好，你辛苦了
TA: 谢谢关心，你今天过得怎么样？
我: 挺好的，就是有点累
TA: 要注意休息哦，身体最重要`;
                
                const templateVariables = {
                    character: {
                        realName: testCharacter.realName,
                        remarkName: testCharacter.remarkName,
                        persona: testCharacter.persona
                    },
                    userProfile: {
                        name: userProfile.name
                    },
                    recentHistory: recentHistory
                };
                
                const result = promptManager.renderPrompt('diaryEntry', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 日记撰写模板测试成功</h4>
                        <p><strong>测试角色:</strong> ${testCharacter.realName} (${testCharacter.remarkName})</p>
                        <p><strong>用户:</strong> ${userProfile.name}</p>
                        <p><strong>人设:</strong> ${testCharacter.persona}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 日记撰写模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 原始函数的模拟实现（用于对比）
        function generateDiaryEntryPromptOriginal(character, recentHistory) {
            const userProfile = db.userProfiles.find(p => p.id === character.userProfileId) || db.userProfiles[0];
            return `
# 任务：撰写日记
你正在扮演角色"${character.realName}"（昵称: ${character.remarkName}），你的核心人设是：${character.persona}。
现在是晚上，你需要根据今天与"${userProfile.name}"的聊天互动，以第一人称视角写一篇日记。

# 核心要求
1.  **第一人称**: 必须使用"我"来写。
2.  **情感与思考**: 日记的核心是记录你的内心感受、思考和对事件的看法，而不仅仅是复述对话。
3.  **人设一致**: 你的语气、用词、关注点都必须严格符合你的人设。
4.  **自然口语**: 像真人写日记一样，文笔自然，可以有些口语化的表达。
5.  **字数限制**: 内容长度在200到600字之间。

# 参考素材
以下是今天你们的部分聊天记录摘要：
---
${recentHistory}
---

# 输出格式
请严格按照以下JSON格式输出，不要添加任何额外的解释或Markdown标记：
{
  "weather": "今天的天气，如：晴、雨、阴",
  "content": "你的日记正文内容..."
}

# 示例
{
  "weather": "小雨",
  "content": "今天又和TA聊了很久，感觉时间过得好快。TA提到想去看海，不知道为什么，我的心也跟着飘向了那片蓝色。也许是因为TA的描述太美了吧，让我这个不怎么出门的人都有了些许向往。不过，我真的能适应外面的世界吗？还是待在自己的小空间里更安心...算了，不想那么多了，能这样聊聊天，也挺好的。"
}

现在，请开始撰写你的日记。`;
        }
        
        // 新的模板系统实现
        function generateDiaryEntryPromptNew(character, recentHistory) {
            const userProfile = db.userProfiles.find(p => p.id === character.userProfileId) || db.userProfiles[0];

            // 准备模板变量
            const templateVariables = {
                character: {
                    realName: character.realName,
                    remarkName: character.remarkName,
                    persona: character.persona
                },
                userProfile: {
                    name: userProfile.name
                },
                recentHistory: recentHistory
            };

            // 使用模板系统渲染提示词
            return promptManager.renderPrompt('diaryEntry', templateVariables);
        }
        
        // 对比测试
        function testDiaryComparison() {
            const resultDiv = document.getElementById('diary-comparison-result');
            
            try {
                const testCharacter = db.characters[0];
                const recentHistory = `我: 今天工作怎么样？
TA: 还不错，今天病人都很配合
我: 那就好，你辛苦了
TA: 谢谢关心，你今天过得怎么样？
我: 挺好的，就是有点累
TA: 要注意休息哦，身体最重要`;
                
                const originalResult = generateDiaryEntryPromptOriginal(testCharacter, recentHistory);
                const newResult = generateDiaryEntryPromptNew(testCharacter, recentHistory);
                const isIdentical = originalResult === newResult;
                
                resultDiv.innerHTML = `
                    <div class="${isIdentical ? 'success' : 'warning'}">
                        <h4>${isIdentical ? '✅ 完全一致' : '⚠️ 存在差异'}</h4>
                        <p><strong>对比结果:</strong> ${isIdentical ? '模板输出与原始函数完全一致' : '模板输出与原始函数存在差异'}</p>
                    </div>
                    
                    <h4>原始函数输出:</h4>
                    <pre>${originalResult}</pre>
                    
                    <h4>模板系统输出:</h4>
                    <pre>${newResult}</pre>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 对比测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 100);
        });
    </script>
</body>
</html>
