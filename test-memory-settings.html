<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Settings Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Memory Settings 测试页面</h1>
    
    <div class="test-section info">
        <h3>📋 测试说明</h3>
        <p>这个页面用于测试 memorySettings 的初始化和迁移逻辑</p>
        <button onclick="runTests()">🚀 运行测试</button>
        <button onclick="clearStorage()">🗑️ 清空存储</button>
        <button onclick="showCurrentState()">👁️ 查看当前状态</button>
    </div>
    
    <div id="test-results"></div>
    
    <script src="src/js/prompt-manager.js"></script>
    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            console.log(message);
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateDisplay();
        }
        
        function updateDisplay() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => `
                <div class="test-section ${result.type}">
                    <strong>[${result.timestamp}]</strong> ${result.message}
                </div>
            `).join('');
        }
        
        function clearStorage() {
            localStorage.removeItem('gemini-chat-app-db');
            window.db = null;
            testResults = [];
            updateDisplay();
            log('✅ 存储已清空', 'success');
        }
        
        function showCurrentState() {
            const state = {
                hasWindowDb: !!window.db,
                hasMemorySettings: !!(window.db && window.db.memorySettings),
                memorySettings: window.db ? window.db.memorySettings : null,
                hasPromptManager: !!window.promptManager,
                isInitialized: window.promptManager ? window.promptManager.isInitialized : false
            };
            
            log(`📊 当前状态:\n${JSON.stringify(state, null, 2)}`, 'info');
        }
        
        async function runTests() {
            testResults = [];
            log('🚀 开始测试...', 'info');
            
            // 测试1: 清空状态下的初始化
            log('📝 测试1: 清空状态下的初始化', 'info');
            clearStorage();
            
            try {
                // 模拟loadData函数
                window.db = JSON.parse(localStorage.getItem('gemini-chat-app-db') || '{}');
                
                // 初始化PromptManager
                window.promptManager = new PromptManager();
                window.promptManager.init();
                
                if (window.db.memorySettings && 
                    window.db.memorySettings.injectionPrompt && 
                    window.db.memorySettings.extractionPrompt) {
                    log('✅ 测试1通过: memorySettings 正确初始化', 'success');
                } else {
                    log('❌ 测试1失败: memorySettings 未正确初始化', 'error');
                    log(`当前值: ${JSON.stringify(window.db.memorySettings)}`, 'error');
                }
            } catch (error) {
                log(`❌ 测试1异常: ${error.message}`, 'error');
            }
            
            // 测试2: 空memorySettings的修复
            log('📝 测试2: 空memorySettings的修复', 'info');
            try {
                window.db.memorySettings = { injectionPrompt: '', extractionPrompt: '' };
                
                window.promptManager = new PromptManager();
                window.promptManager.init();
                
                if (window.db.memorySettings.injectionPrompt && 
                    window.db.memorySettings.extractionPrompt) {
                    log('✅ 测试2通过: 空值被正确填充', 'success');
                } else {
                    log('❌ 测试2失败: 空值未被填充', 'error');
                }
            } catch (error) {
                log(`❌ 测试2异常: ${error.message}`, 'error');
            }
            
            // 测试3: 模板系统迁移
            log('📝 测试3: 模板系统迁移', 'info');
            try {
                const hasMemoryTemplates = window.promptManager.templates.memoryInjection && 
                                         window.promptManager.templates.memoryExtraction;
                
                if (hasMemoryTemplates) {
                    log('✅ 测试3通过: 记忆模板正确创建', 'success');
                } else {
                    log('❌ 测试3失败: 记忆模板未创建', 'error');
                }
            } catch (error) {
                log(`❌ 测试3异常: ${error.message}`, 'error');
            }
            
            log('🏁 测试完成', 'info');
            showCurrentState();
        }
        
        // 页面加载时显示当前状态
        window.addEventListener('load', () => {
            setTimeout(showCurrentState, 1000);
        });
    </script>
</body>
</html>
