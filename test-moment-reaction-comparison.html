<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>朋友圈反应模板对比测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .comparison { display: flex; gap: 20px; }
        .column { flex: 1; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .diff { background-color: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <h1>朋友圈反应模板对比测试</h1>
    
    <div id="test-results"></div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            },
            characters: [
                { id: 'char1', remarkName: '小明' },
                { id: 'char2', remarkName: '小红' }
            ],
            groups: [
                { 
                    id: 'group1', 
                    members: [
                        { originalCharId: 'char1' },
                        { originalCharId: 'char2' }
                    ]
                }
            ]
        };
        
        // 创建PromptManager实例
        const promptManager = new PromptManager();
        promptManager.init();
        
        // 测试数据
        const testReactor = {
            id: 'char2',
            realName: "李小红",
            persona: "温柔体贴的护士"
        };
        
        const testMoment = {
            characterId: 'char1',
            content: "今天天气真好，出门散步了！",
            comments: [
                {
                    characterId: 'char2',
                    content: "确实很不错呢！",
                    replyTo: null
                },
                {
                    characterId: 'user_me',
                    content: "我也想去",
                    replyTo: { name: '小红' }
                }
            ]
        };
        
        const testReplyingToComment = {
            characterId: 'user_me',
            content: "我也想去"
        };
        
        // 原始函数的模拟实现
        function generateMomentReactionPromptOriginal(reactor, momentToReact, replyingToComment = null) {
            const postAuthor = db.characters.find(c => c.id === momentToReact.characterId) || { remarkName: '我', id: 'user_me' };
            
            const reactorGroups = (db.groups || []).filter(g => (g.members || []).some(m => m.originalCharId === reactor.id));
            const authorGroups = (db.groups || []).filter(g => (g.members || []).some(m => m.originalCharId === postAuthor.id));
            const areFriends = reactorGroups.some(rg => authorGroups.some(ag => ag.id === rg.id));

            let prompt = `你正在扮演角色"${reactor.realName}"，你的设定是：${reactor.persona || '一个友好的人'}。\n你正在看"${postAuthor.remarkName}"的朋友圈动态。\n`;

            if (areFriends) {
                prompt += `你和"${postAuthor.remarkName}"在同一个群聊里，你们是朋友。\n`;
            } else if (postAuthor.id !== 'user_me') {
                prompt += `你和"${postAuthor.remarkName}"不在任何共同的群聊中，你们是陌生人。请用更礼貌、通用的方式互动。\n`;
            }

            prompt += `\n--- 动态内容 ---\n${postAuthor.remarkName}: ${momentToReact.content}\n`;
            if ((momentToReact.comments || []).length > 0) {
                prompt += `\n--- 已有评论 ---\n`;
                (momentToReact.comments || []).forEach(comment => { 
                    const commenter = db.characters.find(c => c.id === comment.characterId) || { remarkName: '我', id: 'user_me' }; 
                    if (comment.replyTo) {
                        prompt += `${commenter.remarkName} 回复 ${comment.replyTo.name}: ${comment.content}\n`;
                    } else {
                        prompt += `${commenter.remarkName}: ${comment.content}\n`;
                    }
                });
            }
            prompt += `\n--- 你的任务 ---\n`;
            if (replyingToComment) {
                 const targetCommenter = db.characters.find(c => c.id === replyingToComment.characterId) || { remarkName: '我', id: 'user_me' };
                 prompt += `现在，请你作为"${reactor.realName}"，回复"${targetCommenter.remarkName}"的评论："${replyingToComment.content}"。\n请直接输出你的回复内容，不要带任何前缀或格式。`;
            } else {
                 prompt += `现在，请决定你的行动。你有四个选择：\n1. 点赞和评论: 如果你既想点赞也想评论，请回复格式：[LIKE][COMMENT:你的评论内容]\n2. 只评论: 如果你只想评论，请回复格式：[COMMENT:你的评论内容]\n3. 只点赞或忽略: 如果你只想点赞，或者觉得这条动态不需要互动，请只回复：[LIKE] 或 [IGNORE]\n4. 删除动态(极小概率): 如果你扮演的正好是动态发布者(${postAuthor.remarkName})，并且觉得这条动态不合适，可以回复[DELETE]来删除它。\n你的评论应该非常口语化、简洁，就像真实的朋友圈评论一样。请严格按照以上格式之一进行回复，不要添加任何额外的文字。`;
            }
            return prompt;
        }
        
        // 新的模板系统实现
        function generateMomentReactionPromptNew(reactor, momentToReact, replyingToComment = null) {
            const postAuthor = db.characters.find(c => c.id === momentToReact.characterId) || { remarkName: '我', id: 'user_me' };
            
            const reactorGroups = (db.groups || []).filter(g => (g.members || []).some(m => m.originalCharId === reactor.id));
            const authorGroups = (db.groups || []).filter(g => (g.members || []).some(m => m.originalCharId === postAuthor.id));
            const areFriends = reactorGroups.some(rg => authorGroups.some(ag => ag.id === rg.id));

            // 准备友谊状态文本
            let friendshipStatus = "";
            if (areFriends) {
                friendshipStatus = `你和"${postAuthor.remarkName}"在同一个群聊里，你们是朋友。`;
            } else if (postAuthor.id !== 'user_me') {
                friendshipStatus = `你和"${postAuthor.remarkName}"不在任何共同的群聊中，你们是陌生人。请用更礼貌、通用的方式互动。`;
            }

            // 准备已有评论文本
            let existingComments = "";
            if ((momentToReact.comments || []).length > 0) {
                existingComments = `\n--- 已有评论 ---\n`;
                (momentToReact.comments || []).forEach(comment => { 
                    const commenter = db.characters.find(c => c.id === comment.characterId) || { remarkName: '我', id: 'user_me' }; 
                    if (comment.replyTo) {
                        existingComments += `${commenter.remarkName} 回复 ${comment.replyTo.name}: ${comment.content}\n`;
                    } else {
                        existingComments += `${commenter.remarkName}: ${comment.content}\n`;
                    }
                });
            }

            // 准备任务指令
            let taskInstructions = "";
            if (replyingToComment) {
                 const targetCommenter = db.characters.find(c => c.id === replyingToComment.characterId) || { remarkName: '我', id: 'user_me' };
                 taskInstructions = `现在，请你作为"${reactor.realName}"，回复"${targetCommenter.remarkName}"的评论："${replyingToComment.content}"。\n请直接输出你的回复内容，不要带任何前缀或格式。`;
            } else {
                 taskInstructions = `现在，请决定你的行动。你有四个选择：\n1. 点赞和评论: 如果你既想点赞也想评论，请回复格式：[LIKE][COMMENT:你的评论内容]\n2. 只评论: 如果你只想评论，请回复格式：[COMMENT:你的评论内容]\n3. 只点赞或忽略: 如果你只想点赞，或者觉得这条动态不需要互动，请只回复：[LIKE] 或 [IGNORE]\n4. 删除动态(极小概率): 如果你扮演的正好是动态发布者(${postAuthor.remarkName})，并且觉得这条动态不合适，可以回复[DELETE]来删除它。\n你的评论应该非常口语化、简洁，就像真实的朋友圈评论一样。请严格按照以上格式之一进行回复，不要添加任何额外的文字。`;
            }

            // 准备模板变量
            const templateVariables = {
                reactor: {
                    realName: reactor.realName,
                    persona: reactor.persona || '一个友好的人'
                },
                postAuthor: postAuthor,
                momentToReact: momentToReact,
                friendshipStatus: friendshipStatus,
                existingComments: existingComments,
                taskInstructions: taskInstructions
            };

            // 使用模板系统渲染提示词
            return promptManager.renderPrompt('momentReaction', templateVariables);
        }
        
        // 运行对比测试
        function runComparisonTest() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                // 测试1: 普通反应（无回复评论）
                const originalResult1 = generateMomentReactionPromptOriginal(testReactor, testMoment);
                const newResult1 = generateMomentReactionPromptNew(testReactor, testMoment);
                const isIdentical1 = originalResult1 === newResult1;
                
                // 测试2: 回复评论
                const originalResult2 = generateMomentReactionPromptOriginal(testReactor, testMoment, testReplyingToComment);
                const newResult2 = generateMomentReactionPromptNew(testReactor, testMoment, testReplyingToComment);
                const isIdentical2 = originalResult2 === newResult2;
                
                resultsDiv.innerHTML = `
                    <div class="result ${isIdentical1 && isIdentical2 ? 'success' : 'error'}">
                        <h3>朋友圈反应模板对比测试结果</h3>
                        <p><strong>测试1 (普通反应):</strong> ${isIdentical1 ? '✅ 完全一致' : '❌ 存在差异'}</p>
                        <p><strong>测试2 (回复评论):</strong> ${isIdentical2 ? '✅ 完全一致' : '❌ 存在差异'}</p>
                        <p><strong>总体结果:</strong> ${isIdentical1 && isIdentical2 ? '✅ 完全一致' : '❌ 存在差异'}</p>
                    </div>
                    
                    <h3>测试1: 普通反应对比</h3>
                    <div class="comparison">
                        <div class="column">
                            <h4>原始函数输出</h4>
                            <pre>${originalResult1}</pre>
                        </div>
                        <div class="column">
                            <h4>模板系统输出</h4>
                            <pre>${newResult1}</pre>
                        </div>
                    </div>
                    
                    <h3>测试2: 回复评论对比</h3>
                    <div class="comparison">
                        <div class="column">
                            <h4>原始函数输出</h4>
                            <pre>${originalResult2}</pre>
                        </div>
                        <div class="column">
                            <h4>模板系统输出</h4>
                            <pre>${newResult2}</pre>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>测试失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runComparisonTest, 100);
        });
    </script>
</body>
</html>
