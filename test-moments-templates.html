<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>朋友圈模板测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; }
        .template-output { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>朋友圈模板测试</h1>
    
    <div id="test-results"></div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            }
        };
        
        // 创建PromptManager实例
        const promptManager = new PromptManager();
        promptManager.init();
        
        // 测试数据
        const testCharacter = {
            realName: "张小明",
            persona: "活泼开朗的大学生，喜欢摄影和旅行"
        };
        
        const testReactor = {
            realName: "李小红",
            persona: "温柔体贴的护士"
        };
        
        const testPostAuthor = {
            remarkName: "小明"
        };
        
        const testMoment = {
            content: "今天天气真好，出门散步了！"
        };
        
        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let testResults = [];
            
            try {
                // 测试朋友圈发布模板
                const momentPostTemplate = promptManager.getTemplate('momentPost');
                if (momentPostTemplate) {
                    const postVariables = { character: testCharacter };
                    const renderedPost = promptManager.renderPrompt('momentPost', postVariables);
                    
                    const hasCharacterName = renderedPost.includes('张小明');
                    const hasPersona = renderedPost.includes('活泼开朗的大学生');
                    const hasJSONFormat = renderedPost.includes('{"text":');
                    const hasImagePrompt = renderedPost.includes('image_prompt');
                    const hasMusicUrl = renderedPost.includes('music_url');
                    
                    testResults.push({
                        name: "朋友圈发布模板测试",
                        success: hasCharacterName && hasPersona && hasJSONFormat && hasImagePrompt && hasMusicUrl,
                        details: {
                            "模板存在": !!momentPostTemplate,
                            "角色名替换": hasCharacterName,
                            "人设替换": hasPersona,
                            "JSON格式说明": hasJSONFormat,
                            "图片提示": hasImagePrompt,
                            "音乐链接": hasMusicUrl
                        },
                        output: renderedPost
                    });
                } else {
                    testResults.push({
                        name: "朋友圈发布模板测试",
                        success: false,
                        details: { "错误": "模板不存在" },
                        output: "模板未找到"
                    });
                }
                
                // 测试朋友圈反应模板
                const momentReactionTemplate = promptManager.getTemplate('momentReaction');
                if (momentReactionTemplate) {
                    const reactionVariables = {
                        reactor: testReactor,
                        postAuthor: testPostAuthor,
                        momentToReact: testMoment,
                        friendshipStatus: "你和"小明"在同一个群聊里，你们是朋友。",
                        existingComments: "",
                        taskInstructions: "现在，请决定你的行动。你有四个选择：\\n1. 点赞和评论: 如果你既想点赞也想评论，请回复格式：[LIKE][COMMENT:你的评论内容]"
                    };
                    const renderedReaction = promptManager.renderPrompt('momentReaction', reactionVariables);
                    
                    const hasReactorName = renderedReaction.includes('李小红');
                    const hasAuthorName = renderedReaction.includes('小明');
                    const hasMomentContent = renderedReaction.includes('今天天气真好');
                    const hasTaskInstructions = renderedReaction.includes('[LIKE]');
                    
                    testResults.push({
                        name: "朋友圈反应模板测试",
                        success: hasReactorName && hasAuthorName && hasMomentContent && hasTaskInstructions,
                        details: {
                            "模板存在": !!momentReactionTemplate,
                            "反应者名称": hasReactorName,
                            "作者名称": hasAuthorName,
                            "动态内容": hasMomentContent,
                            "任务指令": hasTaskInstructions
                        },
                        output: renderedReaction
                    });
                } else {
                    testResults.push({
                        name: "朋友圈反应模板测试",
                        success: false,
                        details: { "错误": "模板不存在" },
                        output: "模板未找到"
                    });
                }
                
                // 渲染测试结果
                let html = '';
                testResults.forEach(test => {
                    html += `
                        <div class="test-result ${test.success ? 'success' : 'error'}">
                            <h3>${test.name}</h3>
                            <p><strong>结果:</strong> ${test.success ? '✅ 通过' : '❌ 失败'}</p>
                            <p><strong>详细检查:</strong></p>
                            <ul>
                    `;
                    
                    Object.entries(test.details).forEach(([key, value]) => {
                        html += `<li>${key}: ${value ? '✅' : '❌'}</li>`;
                    });
                    
                    html += `
                            </ul>
                            <p><strong>渲染输出:</strong></p>
                            <div class="template-output">
                                <pre>${test.output}</pre>
                            </div>
                        </div>
                    `;
                });
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        <h3>测试失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 100);
        });
    </script>
</body>
</html>
