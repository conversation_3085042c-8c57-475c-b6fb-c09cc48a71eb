<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主动聊天模板系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-size: 12px; max-height: 300px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>主动聊天模板系统测试</h1>
    
    <div class="test-section">
        <h3>测试环境状态</h3>
        <div id="environment-status"></div>
    </div>
    
    <div class="test-section">
        <h3>私聊主动聊天模板测试</h3>
        <button onclick="testProactiveChat()">测试私聊主动聊天</button>
        <div id="proactive-chat-result"></div>
    </div>
    
    <div class="test-section">
        <h3>群聊主动聊天模板测试</h3>
        <button onclick="testGroupProactiveChat()">测试群聊主动聊天</button>
        <div id="group-proactive-chat-result"></div>
    </div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            },
            userProfiles: [
                {
                    id: 'user1',
                    name: '张三'
                }
            ],
            characters: [
                { 
                    id: 'char1', 
                    realName: '李小红',
                    remarkName: '小红', 
                    persona: '温柔体贴的护士，喜欢烘焙和读书',
                    userProfileId: 'user1',
                    history: [
                        {
                            role: 'user',
                            content: '今天工作怎么样？',
                            timestamp: new Date(Date.now() - 3600000).toISOString()
                        },
                        {
                            role: 'assistant',
                            content: '[李小红的消息：还不错，今天病人都很配合]',
                            timestamp: new Date(Date.now() - 3500000).toISOString()
                        }
                    ]
                }
            ],
            groups: [
                { 
                    id: 'group1',
                    name: '好友群',
                    me: { profileId: 'user1' },
                    members: [
                        { 
                            realName: '张小明',
                            groupNickname: '小明',
                            persona: '阳光开朗的大学生，喜欢运动和摄影'
                        },
                        { 
                            realName: '李小红',
                            groupNickname: '小红',
                            persona: '温柔体贴的护士，喜欢烘焙和读书'
                        },
                        { 
                            realName: '王大力',
                            groupNickname: '大力',
                            persona: '健身教练，热爱运动，性格直爽'
                        }
                    ]
                }
            ],
            moments: [
                {
                    id: 'moment1',
                    characterId: 'user_me',
                    content: '今天天气真好！',
                    comments: [
                        {
                            characterId: 'char1',
                            content: '确实很不错呢！'
                        }
                    ]
                }
            ]
        };
        
        // 创建PromptManager实例
        let promptManager;
        
        // 检查环境状态
        function checkEnvironment() {
            const statusDiv = document.getElementById('environment-status');
            let status = '';
            
            try {
                promptManager = new PromptManager();
                promptManager.init();
                
                // 检查模板是否存在
                const proactiveChatTemplate = promptManager.getTemplate('proactiveChat');
                const groupProactiveChatTemplate = promptManager.getTemplate('groupProactiveChat');
                
                if (proactiveChatTemplate && groupProactiveChatTemplate) {
                    status = `
                        <div class="success">
                            <h4>✅ 环境检查通过</h4>
                            <p>• PromptManager 初始化成功</p>
                            <p>• proactiveChat 模板已加载</p>
                            <p>• groupProactiveChat 模板已加载</p>
                            <p>• 测试数据已准备</p>
                        </div>
                    `;
                } else {
                    status = `
                        <div class="warning">
                            <h4>⚠️ 模板缺失</h4>
                            <p>• proactiveChat 模板: ${proactiveChatTemplate ? '✅' : '❌'}</p>
                            <p>• groupProactiveChat 模板: ${groupProactiveChatTemplate ? '✅' : '❌'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                status = `
                    <div class="error">
                        <h4>❌ 环境检查失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
            
            statusDiv.innerHTML = status;
        }
        
        // 测试私聊主动聊天模板
        function testProactiveChat() {
            const resultDiv = document.getElementById('proactive-chat-result');
            
            try {
                const testCharacter = db.characters[0];
                const userProfile = db.userProfiles[0];
                const currentTime = new Date().toLocaleString('zh-CN', { hour12: false });
                const lastMessage = testCharacter.history[testCharacter.history.length - 1];
                const lastMessageTime = new Date(lastMessage.timestamp).toLocaleString('zh-CN', { hour12: false });
                
                const lastMessages = testCharacter.history.slice(-5).map(m => {
                    const sender = m.role === 'user' ? userProfile.name : testCharacter.realName;
                    const contentMatch = m.content.match(/\[.*?：([\s\S]+?)\]/);
                    const text = contentMatch ? contentMatch[1] : m.content;
                    return `${sender}: ${text}`;
                }).join('\n');
                
                const momentContext = `提示：你最近在朋友圈和"${userProfile.name}"有过互动，可以围绕这个开启话题。动态内容是："今天天气真好！"。`;
                
                const templateVariables = {
                    character: {
                        realName: testCharacter.realName,
                        persona: testCharacter.persona
                    },
                    currentTime: currentTime,
                    lastMessageTime: lastMessageTime,
                    lastMessages: lastMessages,
                    momentContext: momentContext,
                    userProfile: {
                        name: userProfile.name
                    }
                };
                
                const result = promptManager.renderPrompt('proactiveChat', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 私聊主动聊天模板测试成功</h4>
                        <p><strong>测试角色:</strong> ${testCharacter.realName}</p>
                        <p><strong>用户:</strong> ${userProfile.name}</p>
                        <p><strong>当前时间:</strong> ${currentTime}</p>
                        <p><strong>上次对话:</strong> ${lastMessageTime}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 私聊主动聊天模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 测试群聊主动聊天模板
        function testGroupProactiveChat() {
            const resultDiv = document.getElementById('group-proactive-chat-result');
            
            try {
                const testGroup = db.groups[0];
                const userProfile = db.userProfiles[0];
                const currentTime = new Date().toLocaleString('zh-CN', { hour12: false });
                
                const membersList = testGroup.members.map(m => `- ${m.realName} (昵称: ${m.groupNickname}): ${m.persona}`).join('\n');
                
                const templateVariables = {
                    group: {
                        name: testGroup.name
                    },
                    currentTime: currentTime,
                    userProfile: userProfile,
                    membersList: membersList
                };
                
                const result = promptManager.renderPrompt('groupProactiveChat', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 群聊主动聊天模板测试成功</h4>
                        <p><strong>测试群聊:</strong> ${testGroup.name}</p>
                        <p><strong>成员数量:</strong> ${testGroup.members.length}</p>
                        <p><strong>当前时间:</strong> ${currentTime}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 群聊主动聊天模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 100);
        });
    </script>
</body>
</html>
