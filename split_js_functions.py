#!/usr/bin/env python3
"""
JavaScript 函数拆分脚本
将 js.js 文件中的所有函数拆分为独立的文件
"""

import re
import os
import json
from pathlib import Path

def extract_functions_from_js(file_path):
    """从 JavaScript 文件中提取所有函数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    functions = []
    
    # 正则表达式匹配各种函数定义
    patterns = [
        # function functionName() { ... }
        r'(function\s+(\w+)\s*\([^)]*\)\s*\{)',
        # const functionName = () => { ... }
        r'(const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)',
        # const functionName = function() { ... }
        r'(const\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)',
        # let functionName = () => { ... }
        r'(let\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)',
        # var functionName = function() { ... }
        r'(var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)',
        # async function functionName() { ... }
        r'(async\s+function\s+(\w+)\s*\([^)]*\)\s*\{)',
    ]
    
    for pattern in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            func_start = match.start()
            func_name = match.group(2)
            
            # 找到函数的完整内容（包括嵌套的大括号）
            func_content = extract_function_body(content, func_start)
            
            if func_content:
                functions.append({
                    'name': func_name,
                    'content': func_content,
                    'start_pos': func_start
                })
    
    # 按位置排序，避免重复
    functions.sort(key=lambda x: x['start_pos'])
    
    # 去重（同一个函数可能被多个模式匹配到）
    unique_functions = []
    seen_names = set()
    
    for func in functions:
        if func['name'] not in seen_names:
            unique_functions.append(func)
            seen_names.add(func['name'])
    
    return unique_functions

def extract_function_body(content, start_pos):
    """提取函数的完整内容，处理嵌套的大括号"""
    brace_count = 0
    in_string = False
    string_char = None
    escape_next = False
    i = start_pos
    
    # 找到第一个开括号
    while i < len(content) and content[i] != '{':
        i += 1
    
    if i >= len(content):
        return None
    
    start_brace = i
    brace_count = 1
    i += 1
    
    while i < len(content) and brace_count > 0:
        char = content[i]
        
        if escape_next:
            escape_next = False
        elif char == '\\':
            escape_next = True
        elif not in_string:
            if char in ['"', "'", '`']:
                in_string = True
                string_char = char
            elif char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
        else:  # in_string
            if char == string_char:
                in_string = False
                string_char = None
        
        i += 1
    
    if brace_count == 0:
        return content[start_pos:i]
    
    return None

def extract_constants_and_variables(file_path):
    """提取常量和变量定义"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    constants = []
    
    # 匹配各种常量和变量定义
    patterns = [
        # const CONSTANT_NAME = ...;
        r'(const\s+([A-Z_][A-Z0-9_]*)\s*=\s*[^;]+;)',
        # const variableName = ...;
        r'(const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]+;)',
        # let variableName = ...;
        r'(let\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]+;)',
        # var variableName = ...;
        r'(var\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]+;)',
    ]
    
    for pattern in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        for match in matches:
            const_name = match.group(2)
            const_content = match.group(1)
            
            constants.append({
                'name': const_name,
                'content': const_content,
                'start_pos': match.start()
            })
    
    # 去重并排序
    constants.sort(key=lambda x: x['start_pos'])
    unique_constants = []
    seen_names = set()
    
    for const in constants:
        if const['name'] not in seen_names:
            unique_constants.append(const)
            seen_names.add(const['name'])
    
    return unique_constants

def create_output_directories():
    """创建输出目录"""
    directories = [
        'extracted_functions',
        'extracted_functions/functions',
        'extracted_functions/constants',
        'extracted_functions/variables'
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

def save_functions_to_files(functions, output_dir='extracted_functions/functions'):
    """将函数保存到独立文件"""
    for func in functions:
        filename = f"{func['name']}.js"
        filepath = Path(output_dir) / filename
        
        content = f"""// 函数: {func['name']}
// 从 js.js 自动提取

{func['content']}
"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已保存函数: {func['name']} -> {filepath}")

def save_constants_to_files(constants, output_dir='extracted_functions/constants'):
    """将常量保存到独立文件"""
    for const in constants:
        filename = f"{const['name']}.js"
        filepath = Path(output_dir) / filename
        
        content = f"""// 常量/变量: {const['name']}
// 从 js.js 自动提取

{const['content']}
"""
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已保存常量: {const['name']} -> {filepath}")

def generate_summary_report(functions, constants):
    """生成拆分报告"""
    report = {
        'total_functions': len(functions),
        'total_constants': len(constants),
        'functions': [{'name': f['name'], 'size': len(f['content'])} for f in functions],
        'constants': [{'name': c['name'], 'size': len(c['content'])} for c in constants],
        'function_names': [f['name'] for f in functions],
        'constant_names': [c['name'] for c in constants]
    }
    
    # 保存 JSON 报告
    with open('extracted_functions/extraction_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 保存可读报告
    with open('extracted_functions/extraction_report.md', 'w', encoding='utf-8') as f:
        f.write(f"""# JavaScript 函数拆分报告

## 统计信息
- 提取的函数数量: {len(functions)}
- 提取的常量/变量数量: {len(constants)}
- 总计: {len(functions) + len(constants)} 个项目

## 提取的函数列表
""")
        for i, func in enumerate(functions, 1):
            f.write(f"{i}. `{func['name']}()` ({len(func['content'])} 字符)\n")
        
        f.write(f"\n## 提取的常量/变量列表\n")
        for i, const in enumerate(constants, 1):
            f.write(f"{i}. `{const['name']}` ({len(const['content'])} 字符)\n")
    
    print(f"\n拆分完成！")
    print(f"- 提取了 {len(functions)} 个函数")
    print(f"- 提取了 {len(constants)} 个常量/变量")
    print(f"- 报告已保存到 extracted_functions/extraction_report.md")

def main():
    """主函数"""
    js_file_path = 'src/js/js.js'
    
    if not os.path.exists(js_file_path):
        print(f"错误: 找不到文件 {js_file_path}")
        return
    
    print(f"开始分析文件: {js_file_path}")
    
    # 创建输出目录
    create_output_directories()
    
    # 提取函数
    print("正在提取函数...")
    functions = extract_functions_from_js(js_file_path)
    
    # 提取常量和变量
    print("正在提取常量和变量...")
    constants = extract_constants_and_variables(js_file_path)
    
    # 保存到文件
    print("正在保存函数到文件...")
    save_functions_to_files(functions)
    
    print("正在保存常量到文件...")
    save_constants_to_files(constants)
    
    # 生成报告
    generate_summary_report(functions, constants)

if __name__ == '__main__':
    main()