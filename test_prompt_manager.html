<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PromptManager 测试</title>
</head>
<body>
    <h1>PromptManager 测试</h1>
    <div id="output"></div>

    <script src="src/js/prompt-manager.js"></script>
    <script>
        // 测试 PromptManager
        function runTests() {
            const output = document.getElementById('output');
            
            try {
                // 清空 localStorage
                localStorage.removeItem('gemini-chat-app-db');
                
                // 创建 PromptManager 实例
                const promptManager = new PromptManager();
                
                output.innerHTML += '<p>✅ PromptManager 实例创建成功</p>';
                
                // 初始化
                promptManager.init();
                
                output.innerHTML += '<p>✅ PromptManager 初始化成功</p>';
                
                // 检查 localStorage 中的数据
                const dbData = localStorage.getItem('gemini-chat-app-db');
                if (dbData) {
                    const parsedData = JSON.parse(dbData);
                    output.innerHTML += '<p>✅ localStorage 中有数据</p>';
                    output.innerHTML += '<p>数据结构: ' + JSON.stringify(Object.keys(parsedData), null, 2) + '</p>';
                    
                    // 检查是否有 promptCategories
                    if (parsedData.promptCategories) {
                        output.innerHTML += '<p>✅ promptCategories 存在</p>';
                    }
                    
                    // 检查是否有 promptTemplates
                    if (parsedData.promptTemplates) {
                        output.innerHTML += '<p>✅ promptTemplates 存在</p>';
                    }
                    
                    // 检查是否有 memorySettings
                    if (parsedData.memorySettings) {
                        output.innerHTML += '<p>✅ memorySettings 存在</p>';
                    }
                } else {
                    output.innerHTML += '<p>❌ localStorage 中没有数据</p>';
                }
                
                // 测试获取模板
                const categories = promptManager.getCategories();
                if (categories && Object.keys(categories).length > 0) {
                    output.innerHTML += '<p>✅ 成功获取分类: ' + Object.keys(categories).join(', ') + '</p>';
                }
                
                // 测试获取模板
                const templates = promptManager.getAllTemplates();
                if (templates && Object.keys(templates).length > 0) {
                    output.innerHTML += '<p>✅ 成功获取模板: ' + Object.keys(templates).length + ' 个</p>';
                }
                
                output.innerHTML += '<p>🎉 所有测试通过！</p>';
                
            } catch (error) {
                output.innerHTML += '<p>❌ 测试失败: ' + error.message + '</p>';
                console.error('测试错误:', error);
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
