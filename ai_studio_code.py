#!/usr/bin/env python3
"""
JavaScript 代码拆分脚本 - 最终版

本脚本经过多轮迭代，能够处理包含复杂语法（如正则表达式字面量）和
常见代码结构（如 `DOMContentLoaded` 事件监听器）的大型JS文件。

主要特性:
- 健壮的JS解析器，可处理字符串、注释、模板字面量和正则表达式。
- 分层作用域分析，能够深入大型代码块（如DOMContentLoaded）内部提取函数。
- 完整的ES模块语法支持（import/export）。
- 智能的去嵌套和去重逻辑。
- 自动生成拆分后的文件、模块索引（index.js）和详细报告。
"""

import re
import os
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional

# -----------------------------------------------------------------------------
#  1. JAVASCRIPT PARSER CLASS
#  (核心解析引擎，用于精确匹配括号，处理复杂语法)
# -----------------------------------------------------------------------------
class JSParser:
    def __init__(self, content: str):
        self.content = content
        self.length = len(content)
        self.pos = 0
        
    def peek(self, offset: int = 0) -> str:
        pos = self.pos + offset
        return self.content[pos] if pos < self.length else ''
    
    def advance(self, count: int = 1):
        self.pos += count
    
    def get_previous_non_whitespace_char(self) -> str:
        pos = self.pos - 1
        while pos >= 0 and self.content[pos].isspace():
            pos -= 1
        return self.content[pos] if pos >= 0 else ''
    
    def skip_string(self, quote_char: str) -> bool:
        if self.peek() != quote_char: return False
        self.advance()
        while self.pos < self.length:
            char = self.peek()
            if char == '\\': self.advance(2)
            elif char == quote_char: self.advance(); return True
            else: self.advance()
        return False

    def skip_regex_literal(self) -> bool:
        if self.peek() != '/': return False
        
        context_before = self.content[max(0, self.pos - 15):self.pos].strip()
        if context_before.endswith('return'): pass
        elif self.get_previous_non_whitespace_char() not in '(=,[!&|?{};:+*-/%<>^~':
            return False
            
        self.advance()
        while self.pos < self.length:
            char = self.peek()
            if char == '\\': self.advance(2)
            elif char == '/':
                self.advance()
                while self.pos < self.length and self.peek() in 'gimsuy': self.advance()
                return True
            elif char == '\n': return False
            else: self.advance()
        return False

    def skip_template_literal(self) -> bool:
        if self.peek() != '`': return False
        self.advance()
        while self.pos < self.length:
            char = self.peek()
            if char == '\\': self.advance(2)
            elif char == '$' and self.peek(1) == '{':
                self.advance(2)
                brace_count = 1
                while self.pos < self.length and brace_count > 0:
                    c = self.peek()
                    if c in ['"', "'", '`']: self.skip_string(c)
                    elif c == '/':
                        if not self.skip_regex_literal(): self.advance()
                    elif c == '{': brace_count += 1; self.advance()
                    elif c == '}': brace_count -= 1; self.advance()
                    else: self.advance()
            elif char == '`': self.advance(); return True
            else: self.advance()
        return False

    def skip_comment(self) -> bool:
        if self.peek() == '/' and self.peek(1) == '/':
            while self.pos < self.length and self.peek() != '\n': self.advance()
            return True
        elif self.peek() == '/' and self.peek(1) == '*':
            self.advance(2)
            while self.pos < self.length - 1:
                if self.peek() == '*' and self.peek(1) == '/': self.advance(2); return True
                self.advance()
        return False

    def find_matching_brace(self, start_pos: int) -> Optional[int]:
        self.pos = start_pos
        while self.pos < self.length and self.peek() != '{': self.advance()
        if self.pos >= self.length: return None
            
        brace_count = 1
        self.advance()
        
        while self.pos < self.length and brace_count > 0:
            char = self.peek()
            if char in ['"', "'"]: self.skip_string(char)
            elif char == '`': self.skip_template_literal()
            elif char == '/':
                if self.peek(1) in ['/', '*']: self.skip_comment()
                elif not self.skip_regex_literal(): self.advance()
            elif char == '{': brace_count += 1; self.advance()
            elif char == '}':
                brace_count -= 1
                if brace_count == 0: return self.pos
                self.advance()
            else: self.advance()
        return None

# -----------------------------------------------------------------------------
#  2. EXTRACTION FUNCTIONS
#  (这些函数现在接受字符串内容和偏移量，以便在不同作用域内复用)
# -----------------------------------------------------------------------------

def _extract_items_from_string(content: str, parser: JSParser, patterns: List[Tuple[str, str]], item_validator, offset: int = 0) -> List[Dict]:
    """通用项目提取器"""
    items = []
    for pattern, item_type in patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            item_start_abs = match.start() + offset
            
            if item_type in ['default_export_function']:
                item_name = 'default_export'
            elif item_type in ['named_default_export_function']:
                 item_name = match.group(2)
            else:
                item_name = match.group(2)

            end_pos_abs = parser.find_matching_brace(item_start_abs)
            
            if end_pos_abs is not None:
                item_content = parser.content[item_start_abs : end_pos_abs + 1]
                if item_validator(item_content, item_name, item_type):
                    items.append({
                        'name': item_name, 'content': item_content,
                        'start_pos': item_start_abs, 'end_pos': end_pos_abs,
                        'type': item_type, 'size': len(item_content)
                    })
    return items

def _extract_functions_from_string(content: str, parser: JSParser, offset: int = 0) -> List[Dict]:
    """从字符串中提取函数"""
    patterns = [
        (r'((?:export\s+)?function\s+(\w+)\s*\([^)]*\)\s*\{)', 'function_declaration'),
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)', 'arrow_function_const'),
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)', 'function_expression_const'),
        (r'(let\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{)', 'arrow_function_let'),
        (r'(var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*\{)', 'function_expression_var'),
        (r'((?:export\s+)?async\s+function\s+(\w+)\s*\([^)]*\)\s*\{)', 'async_function'),
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*async\s*\([^)]*\)\s*=>\s*\{)', 'async_arrow_const'),
        (r'(export\s+default\s+function\s*\([^)]*\)\s*\{)', 'default_export_function'),
        (r'(export\s+default\s+function\s+(\w+)\s*\([^)]*\)\s*\{)', 'named_default_export_function'),
    ]
    def validator(content, name, type):
        if len(content) < 20: return False
        invalid = {'if', 'for', 'while', 'switch', 'catch', 'try', 'do', 'with', 'forEach', 'map', 'filter', 'then', 'addEventListener'}
        return name.lower() not in invalid
    
    return _extract_items_from_string(content, parser, patterns, validator, offset)

def _extract_objects_from_string(content: str, parser: JSParser, offset: int = 0) -> List[Dict]:
    """从字符串中提取大型对象"""
    patterns = [
        (r'((?:export\s+)?const\s+(\w+)\s*=\s*\{)', 'object_const'),
        (r'(let\s+(\w+)\s*=\s*\{)', 'object_let'),
        (r'(var\s+(\w+)\s*=\s*\{)', 'object_var'),
    ]
    validator = lambda content, name, type: len(content) > 500
    return _extract_items_from_string(content, parser, patterns, validator, offset)

def _extract_constants_from_string(content: str, all_items: List[Dict], offset: int = 0) -> List[Dict]:
    """从字符串中提取顶层常量"""
    constants = []
    item_ranges = [(item['start_pos'], item['end_pos']) for item in all_items if 'end_pos' in item]
    
    patterns = [
        (r'((?:export\s+)?const\s+([A-Z_][A-Z0-9_]*)\s*=\s*[^{;]+;)', 'global_constant'),
        (r'((?:export\s+)?const\s+([a-z][a-zA-Z0-9_]*)\s*=\s*(?![{`])[^;]+;)', 'global_variable'),
    ]
    
    for pattern, const_type in patterns:
        for match in re.finditer(pattern, content, re.MULTILINE | re.DOTALL):
            const_name = match.group(2)
            const_content = match.group(1).strip()
            match_pos_abs = match.start() + offset
            match_end_abs = match.end() + offset
            
            is_inside_item = any(start <= match_pos_abs < end for start, end in item_ranges)
            
            if not is_inside_item and 10 < len(const_content) < 300:
                constants.append({
                    'name': const_name, 'content': const_content,
                    'start_pos': match_pos_abs, 'end_pos': match_end_abs,
                    'type': const_type, 'size': len(const_content)
                })
    return constants

# -----------------------------------------------------------------------------
#  3. UTILITY AND POST-PROCESSING FUNCTIONS
#  (用于去重、去嵌套、保存文件和生成报告)
# -----------------------------------------------------------------------------

def remove_nested_items(items: List[Dict]) -> List[Dict]:
    if not items: return []
    items.sort(key=lambda x: (x['start_pos'], -x.get('end_pos', x['start_pos'])))
    
    unique_items = []
    for item in items:
        is_nested = any(
            existing['start_pos'] <= item['start_pos'] and 
            existing.get('end_pos', existing['start_pos']) >= item.get('end_pos', item['start_pos'])
            for existing in unique_items
        )
        if not is_nested:
            unique_items = [
                existing for existing in unique_items
                if not (item['start_pos'] <= existing['start_pos'] and 
                       item.get('end_pos', item['start_pos']) >= existing.get('end_pos', existing['start_pos']))
            ]
            unique_items.append(item)
    return unique_items

def remove_duplicates_by_name(items: List[Dict]) -> List[Dict]:
    seen_names = set()
    unique_items = []
    for item in items:
        if item['name'] not in seen_names:
            unique_items.append(item)
            seen_names.add(item['name'])
    return unique_items

def create_output_directories(base_dir='extracted_code_final_v2'):
    for sub_dir in ['', 'functions', 'objects', 'constants']:
        Path(base_dir).joinpath(sub_dir).mkdir(parents=True, exist_ok=True)

def save_items_to_files(items: List[Dict], output_dir: str, item_type: str):
    for item in items:
        filename = f"{item['name']}.js"
        filepath = Path(output_dir) / filename
        
        has_export = any(line.strip().startswith('export') for line in item['content'].split('\n'))
        export_line = ""
        if not has_export and item['name'] != 'default_export':
            export_line = f"\nexport {{ {item['name']} }};"
        
        content = f"""// {item_type}: {item['name']}
// 类型: {item.get('type', 'unknown')}
// 大小: {item['size']} 字符
// 从 js.js 自动提取

{item['content']}{export_line}"""
        
        with open(filepath, 'w', encoding='utf-8') as f: f.write(content)
        print(f"  -> 已保存 {item_type}: {item['name']} -> {filepath}")

def generate_index_file(base_dir, functions, objects, constants):
    index_content = "// 自动生成的索引文件\n\n"
    exported_names = []
    
    for func in functions:
        if func['name'] == 'default_export' or func.get('type') == 'named_default_export_function':
            name_to_import = func['name'] if func['name'] != 'default_export' else 'defaultExportedFunction'
            index_content += f"import {name_to_import} from './functions/{func['name']}.js';\n"
            exported_names.append(name_to_import)
        else:
            index_content += f"import {{ {func['name']} }} from './functions/{func['name']}.js';\n"
            exported_names.append(func['name'])

    for item_list, path_name in [(objects, 'objects'), (constants, 'constants')]:
        if item_list: index_content += f"\n// 导入{path_name}\n"
        for item in item_list:
            index_content += f"import {{ {item['name']} }} from './{path_name}/{item['name']}.js';\n"
            exported_names.append(item['name'])
    
    if exported_names:
        index_content += "\n// 导出所有项目\nexport {\n"
        index_content += ",\n".join([f"  {name}" for name in sorted(exported_names)])
        index_content += "\n};\n"
    
    with open(Path(base_dir) / 'index.js', 'w', encoding='utf-8') as f: f.write(index_content)
    print(f"\n✅ 索引文件已生成: {Path(base_dir) / 'index.js'}")

def generate_final_report(base_dir, functions, objects, constants):
    # JSON Report
    report = { 'info': { 'functions': len(functions), 'objects': len(objects), 'constants': len(constants) } }
    report['functions'] = [{'name': f['name'], 'type': f['type'], 'size': f['size']} for f in functions]
    report['objects'] = [{'name': o['name'], 'type': o['type'], 'size': o['size']} for o in objects]
    report['constants'] = [{'name': c['name'], 'type': c['type'], 'size': c['size']} for c in constants]
    with open(Path(base_dir) / 'extraction_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    # Markdown Report
    with open(Path(base_dir) / 'extraction_report.md', 'w', encoding='utf-8') as f:
        f.write("# JavaScript 代码拆分报告 (最终版)\n\n")
        f.write("## 统计信息\n")
        f.write(f"- 提取的函数: {len(functions)}\n")
        f.write(f"- 提取的对象: {len(objects)}\n")
        f.write(f"- 提取的常量: {len(constants)}\n")
        f.write(f"- **总计**: {len(functions) + len(objects) + len(constants)} 个项目\n\n")
        
        for name, items in [("函数", functions), ("对象", objects), ("常量", constants)]:
            if not items: continue
            f.write(f"## 提取的{name}列表\n")
            for i, item in enumerate(items, 1):
                f.write(f"{i}. `{item['name']}` - {item['type']} ({item['size']} 字符)\n")
            f.write("\n")
    print(f"✅ 报告已保存到 {Path(base_dir) / 'extraction_report.md'}")

# -----------------------------------------------------------------------------
#  4. MAIN EXECUTION LOGIC
#  (实现了分层作用域分析)
# -----------------------------------------------------------------------------

def main():
    js_file_path = 'js.js'
    output_base_dir = 'extracted_code_final_v2'
    
    if not os.path.exists(js_file_path):
        print(f"❌ 错误: 找不到文件 {js_file_path}")
        return

    print(f"🚀 开始分析文件: {js_file_path}")
    create_output_directories(output_base_dir)
    
    with open(js_file_path, 'r', encoding='utf-8') as f:
        full_content = f.read()
    
    parser = JSParser(full_content)
    all_extracted_items = []
    
    # 定义需要分析的作用域列表，初始为全局作用域
    scopes_to_analyze = [(full_content, 0)]
    
    # --- 多轮分析 (Multi-pass Analysis) ---
    pass_num = 1
    while scopes_to_analyze:
        current_content, current_offset = scopes_to_analyze.pop(0)
        print(f"\n🔍 分析轮次 {pass_num}: 作用域偏移量 {current_offset}, 大小 {len(current_content)} 字符")

        # 1. 在当前作用域内提取函数、对象和常量
        print("  - 正在提取函数...")
        functions = _extract_functions_from_string(current_content, parser, current_offset)
        print("  - 正在提取大型对象...")
        objects = _extract_objects_from_string(current_content, parser, current_offset)
        
        current_scope_items = functions + objects
        print("  - 正在提取顶层常量...")
        constants = _extract_constants_from_string(current_content, all_extracted_items + current_scope_items, current_offset)
        
        newly_found_items = functions + objects + constants
        all_extracted_items.extend(newly_found_items)
        print(f"  - 在此轮中找到 {len(newly_found_items)} 个项目。")
        
        # 2. 在当前作用域内查找新的、需要深入分析的子作用域
        #    这里我们专门查找 DOMContentLoaded 结构，可以扩展以支持其他模式
        dom_content_pattern = r"document\.addEventListener\('DOMContentLoaded',\s*\(\s*\)\s*=>\s*\{"
        for match in re.finditer(dom_content_pattern, current_content):
            block_start_abs = match.start() + current_offset
            block_end_abs = parser.find_matching_brace(block_start_abs)
            
            if block_end_abs:
                inner_start_abs = full_content.find('{', block_start_abs) + 1
                inner_end_abs = block_end_abs
                inner_content = full_content[inner_start_abs:inner_end_abs]
                
                print(f"  - 发现 'DOMContentLoaded' 子作用域，将加入分析队列...")
                scopes_to_analyze.append((inner_content, inner_start_abs))
        
        pass_num += 1

    # --- 后处理 ---
    print("\n🧹 所有分析完成，开始进行后处理...")
    print("  - 正在移除嵌套项目...")
    all_extracted_items = remove_nested_items(all_extracted_items)
    
    print("  - 正在重新分类和去重...")
    functions = [item for item in all_extracted_items if 'function' in item.get('type', '') or 'arrow' in item.get('type', '')]
    objects = [item for item in all_extracted_items if item.get('type', '').startswith('object')]
    constants = [item for item in all_extracted_items if item.get('type', '') in ['global_constant', 'global_variable']]
    
    functions = remove_duplicates_by_name(functions)
    objects = remove_duplicates_by_name(objects)
    constants = remove_duplicates_by_name(constants)
    
    # --- 保存和报告 ---
    print("\n💾 正在保存文件...")
    save_items_to_files(functions, Path(output_base_dir) / 'functions', '函数')
    save_items_to_files(objects, Path(output_base_dir) / 'objects', '对象')
    save_items_to_files(constants, Path(output_base_dir) / 'constants', '常量')
    
    generate_index_file(output_base_dir, functions, objects, constants)
    generate_final_report(output_base_dir, functions, objects, constants)

    print(f"\n🎉 拆分成功完成！")
    print(f"   - 提取了 {len(functions)} 个函数")
    print(f"   - 提取了 {len(objects)} 个对象")
    print(f"   - 提取了 {len(constants)} 个常量")
    print(f"   - 所有文件已保存到 '{output_base_dir}' 目录中。")

if __name__ == '__main__':
    main()