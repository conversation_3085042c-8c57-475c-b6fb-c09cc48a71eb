/* 提示词管理界面样式 */

/* 主容器 */
.prompt-ui-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 当提示词界面打开时，禁止页面滚动 */
body.prompt-ui-open {
    overflow: hidden;
}

/* 头部 */
.prompt-ui-header {
    background: #2c3e50;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #34495e;
}

.prompt-ui-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.prompt-ui-actions {
    display: flex;
    gap: 10px;
}

/* 主体区域 */
.prompt-ui-body {
    flex: 1;
    display: flex;
    background: #ecf0f1;
    overflow: hidden;
}

/* 侧边栏 */
.prompt-sidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #bdc3c7;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.category-section, .template-section {
    padding: 15px;
}

.category-section {
    border-bottom: 1px solid #ecf0f1;
}

.category-section h3, .template-section h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
}

/* 分类列表 */
.category-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
}

.category-item:hover {
    background: #f8f9fa;
}

.category-item.active {
    background: #3498db;
    color: white;
}

.category-icon {
    margin-right: 8px;
    font-size: 14px;
}

.category-name {
    flex: 1;
}

.category-count {
    background: rgba(0, 0, 0, 0.1);
    color: inherit;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    min-width: 16px;
    text-align: center;
}

.category-item.active .category-count {
    background: rgba(255, 255, 255, 0.2);
}

/* 模板搜索 */
.template-search {
    margin-bottom: 10px;
}

.template-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.2s ease;
}

.template-search input:focus {
    border-color: #3498db;
}

/* 模板列表 */
.template-section {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.template-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.template-item {
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    position: relative;
}

.template-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.1);
}

.template-item.active {
    border-color: #3498db;
    background: #f8f9fa;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.15);
}

.template-header {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.template-icon {
    margin-right: 8px;
    font-size: 16px;
}

.template-name {
    font-weight: 600;
    font-size: 14px;
    color: #2c3e50;
    flex: 1;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #7f8c8d;
    margin-bottom: 8px;
}

.template-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.template-item:hover .template-actions {
    opacity: 1;
}

.btn-icon {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s ease;
}

.btn-icon:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* 主编辑区域 */
.prompt-main {
    flex: 1;
    background: white;
    overflow: hidden;
}

/* 编辑器占位符 */
.editor-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #7f8c8d;
    text-align: center;
}

.placeholder-content {
    max-width: 300px;
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.placeholder-content h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
}

.placeholder-content p {
    margin: 0;
    font-size: 14px;
}

/* 模板编辑器 */
.prompt-editor {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.editor-title {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.template-name-input {
    border: none;
    background: transparent;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    outline: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.template-name-input:not([readonly]):focus {
    background: white;
    box-shadow: 0 0 0 2px #3498db;
}

.template-category-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-core { background: #e74c3c; color: white; }
.category-memory { background: #9b59b6; color: white; }
.category-moments { background: #f39c12; color: white; }
.category-proactive { background: #27ae60; color: white; }
.category-diary { background: #34495e; color: white; }
.category-call { background: #3498db; color: white; }
.category-media { background: #e67e22; color: white; }

.editor-actions {
    display: flex;
    gap: 8px;
}

/* 编辑器主体 */
.editor-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.editor-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.editor-sidebar {
    width: 280px;
    background: #f8f9fa;
    border-left: 1px solid #ecf0f1;
    padding: 20px;
    overflow-y: auto;
}

/* 模板内容区域 */
.template-content-section h4,
.template-variables-section h4,
.template-info-section h4,
.template-help-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
}

.template-content-wrapper {
    position: relative;
}

.template-content-textarea {
    width: 100%;
    min-height: 300px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    outline: none;
    transition: border-color 0.2s ease;
}

.template-content-textarea:focus {
    border-color: #3498db;
}

.template-content-textarea[readonly] {
    background: #f8f9fa;
    color: #2c3e50;
}

.content-stats {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 11px;
    color: #7f8c8d;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

/* 变量列表 */
.variables-list {
    border: 1px solid #ecf0f1;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
}

.variable-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #ecf0f1;
    font-size: 13px;
}

.variable-item:last-child {
    border-bottom: none;
}

.variable-name {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    color: #e74c3c;
    font-weight: 600;
}

.variable-actions {
    display: flex;
    gap: 4px;
}

.no-variables {
    padding: 20px;
    text-align: center;
    color: #7f8c8d;
    font-size: 13px;
}

/* 模板信息 */
.template-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.info-item label {
    font-weight: 600;
    color: #2c3e50;
}

.info-item span {
    color: #7f8c8d;
}

.info-item select {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    outline: none;
}

.info-item select:disabled {
    background: #f8f9fa;
    color: #7f8c8d;
}

/* 帮助内容 */
.help-content {
    font-size: 12px;
    color: #7f8c8d;
}

.help-item {
    margin-bottom: 12px;
}

.help-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 4px;
}

.help-item code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: #e74c3c;
    font-size: 11px;
}

.help-item ul {
    margin: 4px 0 0 16px;
    padding: 0;
}

.help-item li {
    margin-bottom: 2px;
}

/* 预览区域 */
.editor-preview {
    border-top: 1px solid #ecf0f1;
    background: #f8f9fa;
    max-height: 300px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.preview-header {
    padding: 10px 20px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h4 {
    margin: 0;
    font-size: 14px;
    color: #2c3e50;
}

.preview-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px 20px;
}

.preview-result {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    white-space: pre-wrap;
    color: #2c3e50;
}

.variable-highlight {
    background: #fff3cd;
    color: #856404;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-close {
    background: #e74c3c;
    color: white;
}

.btn-close:hover {
    background: #c0392b;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

.btn .icon {
    font-size: 14px;
}

/* 编辑模式样式 */
.prompt-editor.edit-mode .template-name-input {
    background: white;
    border: 1px solid #ddd;
}

.prompt-editor.edit-mode .template-content-textarea {
    background: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .prompt-sidebar {
        width: 250px;
    }
    
    .editor-sidebar {
        width: 240px;
    }
}

@media (max-width: 768px) {
    .prompt-ui-body {
        flex-direction: column;
    }
    
    .prompt-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #bdc3c7;
    }
    
    .editor-body {
        flex-direction: column;
    }
    
    .editor-sidebar {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid #ecf0f1;
    }
}
