<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群聊模板测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; }
        .template-output { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>群聊模板测试</h1>
    
    <div id="test-results"></div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            }
        };
        
        // 创建PromptManager实例
        const promptManager = new PromptManager();
        promptManager.init();
        
        // 模拟群聊测试数据
        const testGroup = {
            name: "测试群聊",
            members: [
                {
                    groupNickname: "小明",
                    realName: "张小明",
                    persona: "活泼开朗的大学生",
                    originalCharId: "char1"
                },
                {
                    groupNickname: "小红",
                    realName: "李小红", 
                    persona: "温柔体贴的护士",
                    originalCharId: "char2"
                }
            ]
        };
        
        const testUserProfile = {
            name: "测试用户"
        };
        
        const testVariables = {
            group: testGroup,
            userProfile: testUserProfile,
            globalMemories: "--- 共享记忆 (所有人都应记住的信息) ---\n- 重要事件: 昨天是小明的生日\n\n",
            momentsContext: "\n--- 朋友圈动态摘要 (请注意区分发布者是你还是我) ---\n- (我) 测试用户 在 2小时前 发布了: \"今天天气真好！\"\n- (群成员) 小明 在 1小时前 发布了: \"准备去图书馆学习\"\n",
            worldBooksContent: "--- 群聊共享设定 (爪印书) ---\n这是一个大学生群聊，大家都是同班同学。\n\n",
            membersList: `
--- 成员: 小明 (真名: 张小明) ---
   - 人设: 活泼开朗的大学生
   - 与"我"(测试用户)的专属记忆: 我们一起参加过社团活动

--- 成员: 小红 (真名: 李小红) ---
   - 人设: 温柔体贴的护士
   - 与"我"(测试用户)的专属记忆: 她曾经帮我处理过伤口`
        };
        
        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                // 测试群聊模板渲染
                const groupTemplate = promptManager.getTemplate('groupChatSystem');
                const renderedPrompt = promptManager.renderPrompt('groupChatSystem', testVariables);
                
                // 检查是否包含关键内容
                const hasGroupName = renderedPrompt.includes('测试群聊');
                const hasUserName = renderedPrompt.includes('测试用户');
                const hasMembersList = renderedPrompt.includes('张小明') && renderedPrompt.includes('李小红');
                const hasMemories = renderedPrompt.includes('共享记忆');
                const hasMoments = renderedPrompt.includes('朋友圈动态');
                const hasWorldBooks = renderedPrompt.includes('群聊共享设定');
                const hasOutputFormat = renderedPrompt.includes('输出格式');
                const hasRedPacket = renderedPrompt.includes('抢红包');
                const hasVideoCall = renderedPrompt.includes('视频通话');
                
                const allTestsPassed = hasGroupName && hasUserName && hasMembersList && 
                                     hasMemories && hasMoments && hasWorldBooks && 
                                     hasOutputFormat && hasRedPacket && hasVideoCall;
                
                resultsDiv.innerHTML = `
                    <div class="test-result ${allTestsPassed ? 'success' : 'error'}">
                        <h3>群聊模板测试结果</h3>
                        <p><strong>总体状态:</strong> ${allTestsPassed ? '✅ 通过' : '❌ 失败'}</p>
                        <p><strong>模板存在:</strong> ${groupTemplate ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>群名替换:</strong> ${hasGroupName ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>用户名替换:</strong> ${hasUserName ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>成员列表:</strong> ${hasMembersList ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>共享记忆:</strong> ${hasMemories ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>朋友圈动态:</strong> ${hasMoments ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>世界书设定:</strong> ${hasWorldBooks ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>输出格式规则:</strong> ${hasOutputFormat ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>红包功能:</strong> ${hasRedPacket ? '✅ 是' : '❌ 否'}</p>
                        <p><strong>视频通话:</strong> ${hasVideoCall ? '✅ 是' : '❌ 否'}</p>
                    </div>
                    
                    <div class="test-result">
                        <h3>渲染后的群聊提示词</h3>
                        <div class="template-output">
                            <pre>${renderedPrompt}</pre>
                        </div>
                    </div>
                    
                    <div class="test-result">
                        <h3>测试数据</h3>
                        <pre>${JSON.stringify(testVariables, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        <h3>测试失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 100);
        });
    </script>
</body>
</html>
