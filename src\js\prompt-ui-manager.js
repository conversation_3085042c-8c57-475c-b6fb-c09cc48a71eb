/**
 * 提示词管理界面管理器
 * 负责创建和管理提示词管理的用户界面
 */
class PromptUIManager extends EventTarget {
    constructor(promptManager) {
        super();
        this.promptManager = promptManager;
        this.currentTemplate = null;
        this.currentCategory = 'all';
        this.isEditing = false;
        this.editor = null;
        
        // 界面元素引用
        this.container = null;
        this.sidebar = null;
        this.mainArea = null;
        this.templateList = null;
        this.categoryList = null;
    }

    /**
     * 初始化界面管理器
     */
    init() {
        console.log('🎨 初始化提示词管理界面...');
        this.createMainInterface();
        this.bindEvents();
        this.loadTemplateCategories();
        this.loadTemplateList();
        console.log('✅ 提示词管理界面初始化完成');
    }

    /**
     * 创建主界面HTML结构
     */
    createMainInterface() {
        // 创建主容器
        this.container = document.createElement('div');
        this.container.id = 'prompt-management-interface';
        this.container.className = 'prompt-ui-container';
        this.container.style.display = 'none'; // 初始隐藏

        this.container.innerHTML = `
            <div class="prompt-ui-header">
                <h2>🎯 AI提示词管理</h2>
                <div class="prompt-ui-actions">
                    <button id="create-template-btn" class="btn btn-primary">
                        <span class="icon">➕</span> 新建模板
                    </button>
                    <button id="import-templates-btn" class="btn btn-secondary">
                        <span class="icon">📥</span> 导入
                    </button>
                    <button id="export-templates-btn" class="btn btn-secondary">
                        <span class="icon">📤</span> 导出
                    </button>
                    <button id="close-prompt-ui-btn" class="btn btn-close">
                        <span class="icon">✖️</span>
                    </button>
                </div>
            </div>
            
            <div class="prompt-ui-body">
                <div class="prompt-sidebar">
                    <div class="category-section">
                        <h3>📂 模板分类</h3>
                        <div id="template-categories" class="category-list"></div>
                    </div>
                    
                    <div class="template-section">
                        <h3>📝 模板列表</h3>
                        <div class="template-search">
                            <input type="text" id="template-search" placeholder="搜索模板..." />
                        </div>
                        <div id="template-list" class="template-list"></div>
                    </div>
                </div>
                
                <div class="prompt-main">
                    <div id="template-editor-area" class="template-editor-area">
                        <div class="editor-placeholder">
                            <div class="placeholder-content">
                                <span class="placeholder-icon">🎯</span>
                                <h3>选择一个模板开始编辑</h3>
                                <p>从左侧列表中选择一个模板，或创建新模板</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(this.container);

        // 获取关键元素引用
        this.sidebar = this.container.querySelector('.prompt-sidebar');
        this.mainArea = this.container.querySelector('.prompt-main');
        this.templateList = this.container.querySelector('#template-list');
        this.categoryList = this.container.querySelector('#template-categories');
        this.editorArea = this.container.querySelector('#template-editor-area');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 关闭按钮
        const closeBtn = this.container.querySelector('#close-prompt-ui-btn');
        closeBtn?.addEventListener('click', () => this.hide());

        // 新建模板按钮
        const createBtn = this.container.querySelector('#create-template-btn');
        createBtn?.addEventListener('click', () => this.createNewTemplate());

        // 导入导出按钮
        const importBtn = this.container.querySelector('#import-templates-btn');
        importBtn?.addEventListener('click', () => this.importTemplates());

        const exportBtn = this.container.querySelector('#export-templates-btn');
        exportBtn?.addEventListener('click', () => this.exportTemplates());

        // 搜索框
        const searchInput = this.container.querySelector('#template-search');
        searchInput?.addEventListener('input', (e) => this.filterTemplates(e.target.value));

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible()) {
                this.hide();
            }
        });
    }

    /**
     * 加载模板分类
     */
    loadTemplateCategories() {
        if (!this.promptManager) return;

        const categories = this.promptManager.getCategories();
        const categoryHTML = `
            <div class="category-item ${this.currentCategory === 'all' ? 'active' : ''}" data-category="all">
                <span class="category-icon">📋</span>
                <span class="category-name">全部模板</span>
                <span class="category-count">${this.promptManager.getTemplateCount()}</span>
            </div>
            ${Object.entries(categories).map(([key, name]) => `
                <div class="category-item ${this.currentCategory === key ? 'active' : ''}" data-category="${key}">
                    <span class="category-icon">${this.getCategoryIcon(key)}</span>
                    <span class="category-name">${name}</span>
                    <span class="category-count">${this.promptManager.getTemplateCountByCategory(key)}</span>
                </div>
            `).join('')}
        `;

        this.categoryList.innerHTML = categoryHTML;

        // 绑定分类点击事件
        this.categoryList.addEventListener('click', (e) => {
            const categoryItem = e.target.closest('.category-item');
            if (categoryItem) {
                const category = categoryItem.dataset.category;
                this.selectCategory(category);
            }
        });
    }

    /**
     * 获取分类图标
     */
    getCategoryIcon(category) {
        const icons = {
            'core': '💬',
            'memory': '🧠',
            'moments': '📸',
            'proactive': '🚀',
            'diary': '📔',
            'call': '📞',
            'media': '🎵'
        };
        return icons[category] || '📝';
    }

    /**
     * 选择分类
     */
    selectCategory(category) {
        this.currentCategory = category;
        
        // 更新分类选中状态
        this.categoryList.querySelectorAll('.category-item').forEach(item => {
            item.classList.toggle('active', item.dataset.category === category);
        });

        // 重新加载模板列表
        this.loadTemplateList();
    }

    /**
     * 加载模板列表
     */
    loadTemplateList() {
        if (!this.promptManager) return;

        const templates = this.currentCategory === 'all' 
            ? this.promptManager.getAllTemplates()
            : this.promptManager.getTemplatesByCategory(this.currentCategory);

        const templateHTML = Object.entries(templates).map(([id, template]) => `
            <div class="template-item ${this.currentTemplate?.id === id ? 'active' : ''}" data-template-id="${id}">
                <div class="template-header">
                    <span class="template-icon">${this.getCategoryIcon(template.category)}</span>
                    <span class="template-name">${template.name}</span>
                </div>
                <div class="template-meta">
                    <span class="template-category">${this.promptManager.getCategories()[template.category] || template.category}</span>
                    <span class="template-variables">${template.variables?.length || 0} 个变量</span>
                </div>
                <div class="template-actions">
                    <button class="btn-icon edit-template" title="编辑">✏️</button>
                    <button class="btn-icon duplicate-template" title="复制">📋</button>
                    <button class="btn-icon delete-template" title="删除">🗑️</button>
                </div>
            </div>
        `).join('');

        this.templateList.innerHTML = templateHTML || '<div class="no-templates">暂无模板</div>';

        // 绑定模板项事件
        this.bindTemplateItemEvents();
    }

    /**
     * 绑定模板项事件
     */
    bindTemplateItemEvents() {
        this.templateList.addEventListener('click', (e) => {
            const templateItem = e.target.closest('.template-item');
            if (!templateItem) return;

            const templateId = templateItem.dataset.templateId;

            if (e.target.classList.contains('edit-template')) {
                this.editTemplate(templateId);
            } else if (e.target.classList.contains('duplicate-template')) {
                this.duplicateTemplate(templateId);
            } else if (e.target.classList.contains('delete-template')) {
                this.deleteTemplate(templateId);
            } else {
                this.selectTemplate(templateId);
            }
        });
    }

    /**
     * 选择模板
     */
    selectTemplate(templateId) {
        const template = this.promptManager.getTemplate(templateId);
        if (!template) return;

        this.currentTemplate = { id: templateId, ...template };

        // 更新模板选中状态
        this.templateList.querySelectorAll('.template-item').forEach(item => {
            item.classList.toggle('active', item.dataset.templateId === templateId);
        });

        // 加载模板编辑器
        this.loadTemplateEditor();
    }

    /**
     * 加载模板编辑器
     */
    loadTemplateEditor() {
        if (!this.currentTemplate) return;

        // 如果编辑器还未创建，创建编辑器
        if (!this.editor) {
            this.editor = new PromptEditor(this);
        }

        // 加载模板到编辑器
        this.editor.loadTemplate(this.currentTemplate);
        
        // 显示编辑器
        this.editorArea.innerHTML = '';
        this.editorArea.appendChild(this.editor.getElement());
    }

    /**
     * 创建新模板
     */
    createNewTemplate() {
        // 实现创建新模板的逻辑
        console.log('创建新模板');
    }

    /**
     * 编辑模板
     */
    editTemplate(templateId) {
        this.selectTemplate(templateId);
        if (this.editor) {
            this.editor.enterEditMode();
        }
    }

    /**
     * 复制模板
     */
    duplicateTemplate(templateId) {
        // 实现复制模板的逻辑
        console.log('复制模板:', templateId);
    }

    /**
     * 删除模板
     */
    deleteTemplate(templateId) {
        // 实现删除模板的逻辑
        console.log('删除模板:', templateId);
    }

    /**
     * 过滤模板
     */
    filterTemplates(searchTerm) {
        // 实现模板搜索过滤
        console.log('搜索模板:', searchTerm);
    }

    /**
     * 导入模板
     */
    importTemplates() {
        // 实现导入模板的逻辑
        console.log('导入模板');
    }

    /**
     * 导出模板
     */
    exportTemplates() {
        // 实现导出模板的逻辑
        console.log('导出模板');
    }

    /**
     * 显示界面
     */
    show() {
        this.container.style.display = 'flex';
        document.body.classList.add('prompt-ui-open');
    }

    /**
     * 隐藏界面
     */
    hide() {
        this.container.style.display = 'none';
        document.body.classList.remove('prompt-ui-open');
    }

    /**
     * 检查界面是否可见
     */
    isVisible() {
        return this.container.style.display !== 'none';
    }

    /**
     * 切换界面显示状态
     */
    toggle() {
        if (this.isVisible()) {
            this.hide();
        } else {
            this.show();
        }
    }
}

// 导出到全局作用域
window.PromptUIManager = PromptUIManager;
