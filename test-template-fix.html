<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板变量修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>模板变量修复测试</h1>
    
    <div id="test-results"></div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            }
        };
        
        // 创建PromptManager实例
        const promptManager = new PromptManager();
        promptManager.init();
        
        // 测试数据
        const testVariables = {
            userProfile: {
                name: "测试用户"
            },
            character: {
                realName: "测试角色",
                status: "在线"
            },
            currentTime: "2025-09-29 14:20:00"
        };
        
        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            
            // 测试1: 简单变量替换
            const simpleTemplate = "当前时间：{{currentTime}}";
            const simpleResult = promptManager.renderPrompt('privateChatSystem', testVariables);
            
            // 测试2: 嵌套属性访问
            const nestedTemplate = "用户名：{{userProfile.name}}，角色名：{{character.realName}}";
            
            // 手动测试嵌套属性
            let testTemplate = nestedTemplate;
            testTemplate = testTemplate.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
                const keys = path.split('.');
                let value = testVariables;
                
                for (const key of keys) {
                    if (value && typeof value === 'object' && key in value) {
                        value = value[key];
                    } else {
                        return '';
                    }
                }
                
                return value != null ? String(value) : '';
            });
            
            // 显示结果
            resultsDiv.innerHTML = `
                <div class="test-result ${simpleResult.includes('{{') ? 'error' : 'success'}">
                    <h3>测试1: 私聊模板渲染</h3>
                    <p><strong>状态:</strong> ${simpleResult.includes('{{') ? '失败 - 仍有未替换的变量' : '成功'}</p>
                    <p><strong>包含未替换变量:</strong> ${simpleResult.includes('{{userProfile.name}}') ? '是' : '否'}</p>
                    <pre>模板片段: ${simpleResult.substring(0, 200)}...</pre>
                </div>
                
                <div class="test-result ${testTemplate.includes('{{') ? 'error' : 'success'}">
                    <h3>测试2: 嵌套属性测试</h3>
                    <p><strong>原始:</strong> ${nestedTemplate}</p>
                    <p><strong>结果:</strong> ${testTemplate}</p>
                    <p><strong>状态:</strong> ${testTemplate.includes('{{') ? '失败' : '成功'}</p>
                </div>
                
                <div class="test-result">
                    <h3>测试数据</h3>
                    <pre>${JSON.stringify(testVariables, null, 2)}</pre>
                </div>
                
                <div class="test-result">
                    <h3>PromptManager状态</h3>
                    <p><strong>初始化状态:</strong> ${promptManager.isInitialized ? '已初始化' : '未初始化'}</p>
                    <p><strong>私聊模板存在:</strong> ${promptManager.getTemplate('privateChatSystem') ? '是' : '否'}</p>
                </div>
            `;
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 100);
        });
    </script>
</body>
</html>
