<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群聊模板对比测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .comparison { display: flex; gap: 20px; }
        .column { flex: 1; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .diff { background-color: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <h1>群聊模板对比测试</h1>
    
    <div id="test-results"></div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            },
            worldBooks: [
                { id: 'wb1', content: '这是一个大学生群聊，大家都是同班同学。' }
            ],
            memoryEntries: [
                { type: 'global', topic: '重要事件', content: '昨天是小明的生日' },
                { type: 'character', characterId: 'char1', content: '我们一起参加过社团活动' },
                { type: 'character', characterId: 'char2', content: '她曾经帮我处理过伤口' }
            ],
            moments: [
                { characterId: 'user_me', content: '今天天气真好！', timestamp: Date.now() - 2 * 60 * 60 * 1000 },
                { characterId: 'char1', content: '准备去图书馆学习', timestamp: Date.now() - 1 * 60 * 60 * 1000 }
            ],
            characters: [
                { id: 'char1', remarkName: '小明' },
                { id: 'char2', remarkName: '小红' }
            ],
            userProfiles: [
                { id: 'user1', name: '测试用户' }
            ]
        };
        
        // 模拟 formatTimeAgo 函数
        window.formatTimeAgo = function(timestamp) {
            const diff = Date.now() - timestamp;
            const hours = Math.floor(diff / (1000 * 60 * 60));
            return `${hours}小时前`;
        };
        
        // 创建PromptManager实例
        const promptManager = new PromptManager();
        promptManager.init();
        
        // 模拟群聊测试数据
        const testGroup = {
            name: "测试群聊",
            worldBookIds: ['wb1'],
            me: { profileId: 'user1' },
            members: [
                {
                    groupNickname: "小明",
                    realName: "张小明",
                    persona: "活泼开朗的大学生",
                    originalCharId: "char1"
                },
                {
                    groupNickname: "小红",
                    realName: "李小红", 
                    persona: "温柔体贴的护士",
                    originalCharId: "char2"
                }
            ]
        };
        
        // 原始函数的模拟实现
        function generateGroupSystemPromptOriginal(group) {
            if (!group) {
                console.error("严重错误：generateGroupSystemPrompt 接收到的 group 对象为 null。");
                return `[系统紧急通知：群聊数据丢失，无法生成回复。]`;
            }
            if (!Array.isArray(group.members)) {
                group.members = []; 
            }

            const worldBooksContent = (() => {
                if (!Array.isArray(db.worldBooks) || !Array.isArray(group.worldBookIds)) return '';
                return group.worldBookIds.map(id => db.worldBooks.find(wb => wb.id === id)).filter(Boolean).map(wb => wb.content).join('\n\n');
            })();
            
            const globalMemories = Array.isArray(db.memoryEntries)
                ? db.memoryEntries.filter(m => m.type === 'global').map(m => `- ${m.topic}: ${m.content}`).join('\n')
                : '';
            
            let memoryPrompt = "";
            if (globalMemories) {
                memoryPrompt += "--- 共享记忆 (所有人都应记住的信息) ---\n" + globalMemories + "\n\n";
            }
            
            const userProfile = db.userProfiles.find(p => p.id === group.me?.profileId) || db.userProfiles[0];

            let prompt = `你正在一个名为"${group.name}"的群聊里进行角色扮演。请严格遵守以下规则：\n`;
            prompt += `1. **核心任务**: 你需要同时扮演这个群聊中的 **所有** AI 成员。我会作为唯一的人类用户（"${userProfile.name}"）与你们互动。\n`;
            prompt += `2. **群聊成员列表与专属记忆**: 以下是你要扮演的所有角色，以及他们与"我"(${userProfile.name})的个人专属记忆。这些记忆只有对应的角色自己知道，请在对话中自然地体现出来。\n`;
            
            group.members.forEach(member => {
                prompt += `\n--- 成员: ${member.groupNickname} (真名: ${member.realName}) ---\n`;
                prompt += `   - 人设: ${member.persona || '无特定人设'}\n`;
                const charData = db.characters.find(c => c.id === member.originalCharId);
                if (charData) {
                    const characterMemory = Array.isArray(db.memoryEntries)
                        ? db.memoryEntries.find(m => m.type === 'character' && m.characterId === charData.id)
                        : null;
                    if (characterMemory && characterMemory.content) {
                        prompt += `   - 与"我"(${userProfile.name})的专属记忆:\n${characterMemory.content}\n`;
                    } else {
                        prompt += `   - 与"我"(${userProfile.name})的专属记忆: 无\n`;
                    }
                }
            });

            prompt += `\n${memoryPrompt}`;

            let momentsContext = "\n--- 朋友圈动态摘要 (请注意区分发布者是你还是我) ---\n";
            if (Array.isArray(db.moments) && db.moments.length > 0) {
                db.moments.slice(0, 5).forEach(moment => {
                    const author = db.characters.find(c => c.id === moment.characterId);
                    const authorIsMe = moment.characterId === 'user_me';
                    
                    if (author || authorIsMe) {
                         const authorName = authorIsMe ? userProfile.name : author.remarkName;
                         const authorPrefix = authorIsMe ? `(我) ${authorName}` : `(群成员) ${authorName}`;
                         momentsContext += `- ${authorPrefix} 在 ${formatTimeAgo(moment.timestamp)} 发布了: "${moment.content}"\n`;
                    }
                });
            } else {
                momentsContext += "最近朋友圈没有新动态。\n";
            }
            prompt += momentsContext;
            
            if (worldBooksContent) {
                prompt += `--- 群聊共享设定 (爪印书) ---\n${worldBooksContent}\n\n`;
            }
            prompt += `3. **输出格式**: 你生成的每一条消息都 **必须** 严格遵循格式 \`[{成员真名}的消息：{消息内容}]\`。这是唯一的合法格式。请用成员的 **真名** 填充。\n`;
            prompt += `   - 正确示例: [张三的消息：大家好啊！]\n\n`;
            prompt += `4. **模拟群聊氛围**: 为了让群聊看起来真实、活跃且混乱，你的每一次回复都必须遵循以下随机性要求：\n`;
            prompt += `   - **消息数量**: 每次生成 **10到20条** 消息。\n`;
            prompt += `   - **发言者随机**: 随机选择群成员进行发言，可以有的人多说几句，有的暂时沉默。\n`;
            prompt += `   - **对话连贯性**: 对话内容应整体围绕我和其他成员的发言展开，保持逻辑连贯性。\n\n`;
            prompt += `5. **行为准则**:\n`;
            prompt += `   - 严格扮演每个角色的人设，并利用他们的专属记忆来回应相关话题。\n`;
            prompt += `   - 我（用户）可能会发送如 \`[表情包]\`、\`[语音]\`、\`[红包]\` 等特殊消息，或发送 \`[xx邀请xx加入了群聊]\` 或 \`[xx修改群名为：xxx]\` 这样的系统通知，你需要理解这些消息的含义并让群成员做出相应反应。\n`;
            prompt += `   - **抢红包**: 如果我发了红包，想抢红包的角色需要发送一条格式为 \`[{成员真名}领取了${userProfile.name}的红包]\` 的指令。这条指令会触发抢红包成功的效果。\n`;
            prompt += `   - 角色们偶尔也会犯错，可以撤回自己刚刚发出的消息。操作方式是：发送一条格式为 \`[{成员真名}撤回了一条消息]\` 的指令。\n`;
            prompt += `   - **视频通话**: 当我发起群视频时，你会收到一条 \`[系统指令：用户...发起了群组视频通话请求。]\` 的指令。你需要让每个AI成员独立决策，并通过发送 \`[{成员真名}接受视频通话]\` 或 \`[{成员真名}拒绝视频通话]\` 格式的消息来回应。\n`;
            prompt += `   - 保持对话的持续性，不要主动结束对话。\n\n`;
            prompt += `现在，请根据以上设定，开始扮演群聊中的所有角色。`;
            return prompt;
        }
        
        // 新的模板系统实现
        function generateGroupSystemPromptNew(group) {
            if (!group) {
                console.error("严重错误：generateGroupSystemPrompt 接收到的 group 对象为 null。");
                return `[系统紧急通知：群聊数据丢失，无法生成回复。]`;
            }
            if (!Array.isArray(group.members)) {
                group.members = []; 
            }

            // 准备世界书内容
            const worldBooksContent = (() => {
                if (!Array.isArray(db.worldBooks) || !Array.isArray(group.worldBookIds)) return '';
                return group.worldBookIds.map(id => db.worldBooks.find(wb => wb.id === id)).filter(Boolean).map(wb => wb.content).join('\n\n');
            })();
            
            // 准备全局记忆
            const globalMemories = Array.isArray(db.memoryEntries)
                ? db.memoryEntries.filter(m => m.type === 'global').map(m => `- ${m.topic}: ${m.content}`).join('\n')
                : '';
            
            let globalMemoriesContent = "";
            if (globalMemories) {
                globalMemoriesContent = "--- 共享记忆 (所有人都应记住的信息) ---\n" + globalMemories + "\n\n";
            }
            
            // 准备用户资料
            const userProfile = db.userProfiles.find(p => p.id === group.me?.profileId) || db.userProfiles[0];

            // 准备成员列表信息
            let membersList = "";
            group.members.forEach(member => {
                membersList += `\n--- 成员: ${member.groupNickname} (真名: ${member.realName}) ---\n`;
                membersList += `   - 人设: ${member.persona || '无特定人设'}\n`;
                const charData = db.characters.find(c => c.id === member.originalCharId);
                if (charData) {
                    const characterMemory = Array.isArray(db.memoryEntries)
                        ? db.memoryEntries.find(m => m.type === 'character' && m.characterId === charData.id)
                        : null;
                    if (characterMemory && characterMemory.content) {
                        membersList += `   - 与"我"(${userProfile.name})的专属记忆:\n${characterMemory.content}\n`;
                    } else {
                        membersList += `   - 与"我"(${userProfile.name})的专属记忆: 无\n`;
                    }
                }
            });

            // 准备朋友圈动态
            let momentsContext = "\n--- 朋友圈动态摘要 (请注意区分发布者是你还是我) ---\n";
            if (Array.isArray(db.moments) && db.moments.length > 0) {
                db.moments.slice(0, 5).forEach(moment => {
                    const author = db.characters.find(c => c.id === moment.characterId);
                    const authorIsMe = moment.characterId === 'user_me';
                    
                    if (author || authorIsMe) {
                         const authorName = authorIsMe ? userProfile.name : author.remarkName;
                         const authorPrefix = authorIsMe ? `(我) ${authorName}` : `(群成员) ${authorName}`;
                         momentsContext += `- ${authorPrefix} 在 ${formatTimeAgo(moment.timestamp)} 发布了: "${moment.content}"\n`;
                    }
                });
            } else {
                momentsContext += "最近朋友圈没有新动态。\n";
            }

            // 准备世界书内容（包含格式）
            let formattedWorldBooksContent = "";
            if (worldBooksContent) {
                formattedWorldBooksContent = `--- 群聊共享设定 (爪印书) ---\n${worldBooksContent}\n\n`;
            }

            // 准备模板变量
            const templateVariables = {
                group: group,
                userProfile: userProfile,
                globalMemories: globalMemoriesContent,
                momentsContext: momentsContext,
                worldBooksContent: formattedWorldBooksContent,
                membersList: membersList
            };

            // 使用模板系统渲染提示词
            return promptManager.renderPrompt('groupChatSystem', templateVariables);
        }
        
        // 运行对比测试
        function runComparisonTest() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                const originalResult = generateGroupSystemPromptOriginal(testGroup);
                const newResult = generateGroupSystemPromptNew(testGroup);
                
                const isIdentical = originalResult === newResult;
                
                resultsDiv.innerHTML = `
                    <div class="result ${isIdentical ? 'success' : 'error'}">
                        <h3>对比测试结果</h3>
                        <p><strong>结果:</strong> ${isIdentical ? '✅ 完全一致' : '❌ 存在差异'}</p>
                        <p><strong>原始长度:</strong> ${originalResult.length} 字符</p>
                        <p><strong>新版长度:</strong> ${newResult.length} 字符</p>
                    </div>
                    
                    <div class="comparison">
                        <div class="column">
                            <h3>原始函数输出</h3>
                            <pre>${originalResult}</pre>
                        </div>
                        <div class="column">
                            <h3>模板系统输出</h3>
                            <pre>${newResult}</pre>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>测试失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runComparisonTest, 100);
        });
    </script>
</body>
</html>
