<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通话和媒体模板系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-size: 12px; max-height: 400px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>通话和媒体模板系统测试</h1>
    
    <div class="test-section">
        <h3>测试环境状态</h3>
        <div id="environment-status"></div>
    </div>
    
    <div class="test-section">
        <h3>群组视频通话模板测试</h3>
        <button onclick="testGroupVideoCall()">测试群组视频通话</button>
        <div id="group-video-call-result"></div>
    </div>
    
    <div class="test-section">
        <h3>私聊视频通话模板测试</h3>
        <button onclick="testPrivateVideoCall()">测试私聊视频通话</button>
        <div id="private-video-call-result"></div>
    </div>
    
    <div class="test-section">
        <h3>一起听歌回应模板测试</h3>
        <button onclick="testListenTogetherResponse()">测试一起听歌回应</button>
        <div id="listen-together-response-result"></div>
    </div>
    
    <script src="./src/js/prompt-manager.js"></script>
    <script>
        // 初始化测试数据
        window.db = {
            promptCategories: {},
            promptTemplates: {},
            memorySettings: {
                injectionPrompt: "测试记忆注入模板",
                extractionPrompt: "测试记忆提取模板"
            },
            userProfiles: [
                {
                    id: 'user1',
                    name: '张三',
                    persona: '友善的用户'
                }
            ],
            characters: [
                { 
                    id: 'char1', 
                    realName: '李小红',
                    remarkName: '小红', 
                    persona: '温柔体贴的护士，喜欢烘焙和读书，内心细腻敏感',
                    userProfileId: 'user1'
                },
                { 
                    id: 'char2', 
                    realName: '王小明',
                    remarkName: '小明', 
                    persona: '阳光开朗的大学生，热爱运动和音乐',
                    userProfileId: 'user1'
                }
            ],
            groups: [
                {
                    id: 'group1',
                    name: '好友群',
                    members: [
                        { 
                            id: 'char1', 
                            realName: '李小红',
                            groupNickname: '小红护士',
                            persona: '温柔体贴的护士，喜欢烘焙和读书，内心细腻敏感'
                        },
                        { 
                            id: 'char2', 
                            realName: '王小明',
                            groupNickname: '运动达人',
                            persona: '阳光开朗的大学生，热爱运动和音乐'
                        }
                    ],
                    me: { profileId: 'user1' }
                }
            ]
        };
        
        // 创建PromptManager实例
        let promptManager;
        
        // 检查环境状态
        function checkEnvironment() {
            const statusDiv = document.getElementById('environment-status');
            let status = '';
            
            try {
                promptManager = new PromptManager();
                promptManager.init();
                
                // 检查模板是否存在
                const groupVideoCallTemplate = promptManager.getTemplate('groupVideoCall');
                const privateVideoCallTemplate = promptManager.getTemplate('privateVideoCall');
                const listenTogetherResponseTemplate = promptManager.getTemplate('listenTogetherResponse');
                
                if (groupVideoCallTemplate && privateVideoCallTemplate && listenTogetherResponseTemplate) {
                    status = `
                        <div class="success">
                            <h4>✅ 环境检查通过</h4>
                            <p>• PromptManager 初始化成功</p>
                            <p>• groupVideoCall 模板已加载</p>
                            <p>• privateVideoCall 模板已加载</p>
                            <p>• listenTogetherResponse 模板已加载</p>
                            <p>• 测试数据已准备</p>
                        </div>
                    `;
                } else {
                    status = `
                        <div class="warning">
                            <h4>⚠️ 模板缺失</h4>
                            <p>• groupVideoCall 模板: ${groupVideoCallTemplate ? '✅' : '❌'}</p>
                            <p>• privateVideoCall 模板: ${privateVideoCallTemplate ? '✅' : '❌'}</p>
                            <p>• listenTogetherResponse 模板: ${listenTogetherResponseTemplate ? '✅' : '❌'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                status = `
                    <div class="error">
                        <h4>❌ 环境检查失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
            
            statusDiv.innerHTML = status;
        }
        
        // 测试群组视频通话模板
        function testGroupVideoCall() {
            const resultDiv = document.getElementById('group-video-call-result');
            
            try {
                const testGroup = db.groups[0];
                const userProfile = db.userProfiles[0];
                const participants = ['张三', '李小红', '王小明'];
                const callHistory = `张三: 大家好！
李小红: 你好呀！
王小明: 嗨，大家都在啊！`;
                
                const membersList = testGroup.members.map(m => `- ${m.realName} (昵称: ${m.groupNickname}): ${m.persona}`).join('\n');
                
                const templateVariables = {
                    chat: {
                        name: testGroup.name
                    },
                    userProfile: {
                        name: userProfile.name
                    },
                    participants: participants.join('、 '),
                    membersList: membersList,
                    callHistory: callHistory
                };
                
                const result = promptManager.renderPrompt('groupVideoCall', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 群组视频通话模板测试成功</h4>
                        <p><strong>测试群组:</strong> ${testGroup.name}</p>
                        <p><strong>参与者:</strong> ${participants.join('、 ')}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 群组视频通话模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 测试私聊视频通话模板
        function testPrivateVideoCall() {
            const resultDiv = document.getElementById('private-video-call-result');
            
            try {
                const testCharacter = db.characters[0];
                const userProfile = db.userProfiles[0];
                const callHistory = `张三: 你好！
李小红: 嗨，你今天怎么样？
张三: 还不错，你呢？`;
                
                const templateVariables = {
                    chat: {
                        realName: testCharacter.realName,
                        persona: testCharacter.persona
                    },
                    userProfile: {
                        name: userProfile.name,
                        persona: userProfile.persona
                    },
                    callHistory: callHistory
                };
                
                const result = promptManager.renderPrompt('privateVideoCall', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 私聊视频通话模板测试成功</h4>
                        <p><strong>测试角色:</strong> ${testCharacter.realName} (${testCharacter.remarkName})</p>
                        <p><strong>用户:</strong> ${userProfile.name}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 私聊视频通话模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 测试一起听歌回应模板
        function testListenTogetherResponse() {
            const resultDiv = document.getElementById('listen-together-response-result');
            
            try {
                const testCharacter = db.characters[0];
                const userProfile = db.userProfiles[0];
                const testSong = {
                    title: '夜空中最亮的星'
                };
                
                const templateVariables = {
                    character: {
                        realName: testCharacter.realName
                    },
                    userProfile: {
                        name: userProfile.name
                    },
                    song: {
                        title: testSong.title
                    }
                };
                
                const result = promptManager.renderPrompt('listenTogetherResponse', templateVariables);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 一起听歌回应模板测试成功</h4>
                        <p><strong>测试角色:</strong> ${testCharacter.realName} (${testCharacter.remarkName})</p>
                        <p><strong>用户:</strong> ${userProfile.name}</p>
                        <p><strong>歌曲:</strong> ${testSong.title}</p>
                        <p><strong>生成的提示词:</strong></p>
                        <pre>${result}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 一起听歌回应模板测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载完成后检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 100);
        });
    </script>
</body>
</html>
